import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:breakingbright/providers/settings_provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Einstellungen'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context, true); // Always return true since settings might have changed
          },
        ),
      ),
      body: ListView(
        children: [
          _buildLookbackHoursSetting(context),
          const Divider(),
          _buildLlmScoreSetting(context),
        ],
      ),
    );
  }

  Widget _buildLookbackHoursSetting(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Zeitraum für Nachrichten',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '<PERSON>eige Nachrichten der letzten ${settings.lookbackHours} Stunden',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Slider(
                value: settings.lookbackHours.toDouble(),
                min: 24,
                max: 168, // 1 week
                divisions: 6,
                label: '${settings.lookbackHours}h',
                onChanged: (value) {
                  settings.setLookbackHours(value.round());
                },
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: const [
                  Text('24h'),
                  Text('168h'),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLlmScoreSetting(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Positivitätsbewertung anzeigen',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Zeigt die KI-Positivitätsbewertung als Prozentsatz auf den Nachrichtenkarten an. Tippen Sie auf die Bewertung, um die Begründung zu sehen.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('Bewertung anzeigen'),
                subtitle: Text(
                  settings.showLlmScore
                    ? 'Positivitätsbewertung wird angezeigt'
                    : 'Positivitätsbewertung ist ausgeblendet',
                ),
                value: settings.showLlmScore,
                onChanged: (value) {
                  settings.setShowLlmScore(value);
                },
                contentPadding: EdgeInsets.zero,
              ),
            ],
          ),
        );
      },
    );
  }
}







