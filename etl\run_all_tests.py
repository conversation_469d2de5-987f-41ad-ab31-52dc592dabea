"""
Test runner for all NewsAggregator tests
Runs basic functionality tests, integration tests, and provides a summary
"""

import unittest
import sys
import os

# Add paths for imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
etl_path = os.path.dirname(__file__)
sys.path.append(etl_path)


def run_all_tests():
    """Run all test suites and provide summary"""
    
    print("=" * 60)
    print("RUNNING COMPREHENSIVE NEWSAGGREGATOR TEST SUITE")
    print("=" * 60)
    
    # Create test loader
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test modules
    try:
        # Basic functionality tests
        from test_simple import TestNewsAggregatorBasics, TestMockingPatterns
        suite.addTests(loader.loadTestsFromTestCase(TestNewsAggregatorBasics))
        suite.addTests(loader.loadTestsFromTestCase(TestMockingPatterns))
        print("✓ Loaded basic functionality tests")
        
        # Integration tests
        from test_integration import TestNewsAggregatorIntegration, TestHelperFunctions
        suite.addTests(loader.loadTestsFromTestCase(TestNewsAggregatorIntegration))
        suite.addTests(loader.loadTestsFromTestCase(TestHelperFunctions))
        print("✓ Loaded integration tests")
        
        # Advanced tests
        from test_advanced import (TestConfigurationValidation, TestErrorHandling, 
                                 TestEdgeCases, TestDataProcessing, 
                                 TestConcurrencyAndThreading, TestPerformanceConsiderations)
        suite.addTests(loader.loadTestsFromTestCase(TestConfigurationValidation))
        suite.addTests(loader.loadTestsFromTestCase(TestErrorHandling))
        suite.addTests(loader.loadTestsFromTestCase(TestEdgeCases))
        suite.addTests(loader.loadTestsFromTestCase(TestDataProcessing))
        suite.addTests(loader.loadTestsFromTestCase(TestConcurrencyAndThreading))
        suite.addTests(loader.loadTestsFromTestCase(TestPerformanceConsiderations))
        print("✓ Loaded advanced tests (error handling, edge cases, performance)")
        
        # External service tests
        from test_external_services import (TestRSSFeedProcessing, TestTranslationService,
                                          TestMLModelIntegration, TestLLMIntegration,
                                          TestWebScrapingIntegration)
        suite.addTests(loader.loadTestsFromTestCase(TestRSSFeedProcessing))
        suite.addTests(loader.loadTestsFromTestCase(TestTranslationService))
        suite.addTests(loader.loadTestsFromTestCase(TestMLModelIntegration))
        suite.addTests(loader.loadTestsFromTestCase(TestLLMIntegration))
        suite.addTests(loader.loadTestsFromTestCase(TestWebScrapingIntegration))
        print("✓ Loaded external service tests (RSS, translation, ML, LLM, scraping)")
        
        # Database operation tests
        from test_database_operations import (TestDatabaseConnections, TestDatabaseQueries,
                                            TestDatabaseTransactions, TestDataFrameOperations,
                                            TestMultiprocessingOperations, TestDatabasePerformance)
        suite.addTests(loader.loadTestsFromTestCase(TestDatabaseConnections))
        suite.addTests(loader.loadTestsFromTestCase(TestDatabaseQueries))
        suite.addTests(loader.loadTestsFromTestCase(TestDatabaseTransactions))
        suite.addTests(loader.loadTestsFromTestCase(TestDataFrameOperations))
        suite.addTests(loader.loadTestsFromTestCase(TestMultiprocessingOperations))
        suite.addTests(loader.loadTestsFromTestCase(TestDatabasePerformance))
        print("✓ Loaded database operation tests (connections, queries, transactions)")
        
        # Test fixes and improvements
        from test_fixes import (TestFixedExternalServices, TestImprovedMocking,
                              TestPlatformSpecificHandling, TestErrorRecoveryPatterns,
                              TestDataValidationPatterns)
        suite.addTests(loader.loadTestsFromTestCase(TestFixedExternalServices))
        suite.addTests(loader.loadTestsFromTestCase(TestImprovedMocking))
        suite.addTests(loader.loadTestsFromTestCase(TestPlatformSpecificHandling))
        suite.addTests(loader.loadTestsFromTestCase(TestErrorRecoveryPatterns))
        suite.addTests(loader.loadTestsFromTestCase(TestDataValidationPatterns))
        print("✓ Loaded test fixes and improvements (platform handling, error recovery)")
        
    except ImportError as e:
        print(f"⚠ Warning: Could not load some tests: {e}")
        print(f"   This might be due to missing dependencies or import issues.")
        print(f"   Individual test files can still be run separately.")
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
    passed = total_tests - failures - errors - skipped
    
    print(f"Total tests run: {total_tests}")
    print(f"✓ Passed: {passed}")
    print(f"✗ Failed: {failures}")
    print(f"⚠ Errors: {errors}")
    print(f"⊘ Skipped: {skipped}")
    
    if failures > 0:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if errors > 0:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback.split('Error:')[-1].strip()}")
    
    success_rate = (passed / total_tests * 100) if total_tests > 0 else 0
    print(f"\nSuccess rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 Test suite is in good shape!")
    elif success_rate >= 60:
        print("⚠ Test suite needs some attention")
    else:
        print("❌ Test suite needs significant work")
    
    return result.wasSuccessful()


def test_coverage_report():
    """Provide a report on what functionality is covered by tests"""
    
    print("\n" + "=" * 60)
    print("TEST COVERAGE REPORT")
    print("=" * 60)
    
    covered_functionality = [
        "✓ Entry ID generation (SHA256 hashing)",
        "✓ HTML content cleaning (BeautifulSoup)",
        "✓ YAML configuration loading",
        "✓ DataFrame operations (pandas)",
        "✓ Datetime operations with timezone",
        "✓ Text processing and truncation",
        "✓ Mock feedparser responses",
        "✓ Mock database sessions",
        "✓ Context manager patterns",
        "✓ NewsAggregator initialization",
        "✓ RSS source loading",
        "✓ Session scope context manager",
        "✓ Exception handling in sessions",
        "✓ Configuration validation and error handling",
        "✓ Database connection management",
        "✓ Query building and filtering",
        "✓ Transaction handling (commit/rollback)",
        "✓ Edge cases and boundary conditions",
        "✓ Unicode and special character handling",
        "✓ RSS feed parsing (various scenarios)",
        "✓ Translation service integration (mocked)",
        "✓ ML model integration (mocked)",
        "✓ LLM API integration (mocked)",
        "✓ Web scraping integration (mocked)",
        "✓ Multiprocessing patterns",
        "✓ Performance considerations",
        "✓ Memory usage patterns",
        "✓ Concurrency and threading",
        "✓ Data serialization for multiprocessing",
        "✓ Temporary file handling",
        "✓ Batch processing patterns",
        "✓ Database performance optimization",
        "✓ Platform-specific handling (Windows/Unix)",
        "✓ Import error handling and graceful degradation",
        "✓ Mock object string conversion",
        "✓ Improved mocking strategies",
        "✓ Error recovery patterns (retry, circuit breaker)",
        "✓ Data validation and sanitization",
        "✓ Type coercion and input validation",
        "✓ Cross-platform temporary file handling",
    ]
    
    not_covered = [
        "⚠ End-to-end integration with real services",
        "⚠ Actual ML model loading and inference",
        "⚠ Real database integration tests",
        "⚠ Flask dashboard integration",
        "⚠ Production deployment scenarios",
        "⚠ Load testing with real data volumes",
        "⚠ Network failure recovery",
        "⚠ Data corruption handling",
    ]
    
    print("COVERED FUNCTIONALITY:")
    for item in covered_functionality:
        print(f"  {item}")
    
    print("\nNOT FULLY COVERED:")
    for item in not_covered:
        print(f"  {item}")
    
    coverage_percentage = len(covered_functionality) / (len(covered_functionality) + len(not_covered)) * 100
    print(f"\nEstimated coverage: {coverage_percentage:.1f}%")
    
    print("\nRECOMMENDATIONS FOR FURTHER IMPROVEMENT:")
    print("  • Add integration tests with real test database")
    print("  • Implement load testing with large datasets")
    print("  • Add network failure simulation and recovery tests")
    print("  • Test with actual ML models in isolated environment")
    print("  • Add monitoring and alerting test scenarios")
    print("  • Test data migration and schema evolution")
    print("  • Add security testing for API endpoints")
    print("  • Implement chaos engineering tests")


if __name__ == '__main__':
    success = run_all_tests()
    test_coverage_report()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)