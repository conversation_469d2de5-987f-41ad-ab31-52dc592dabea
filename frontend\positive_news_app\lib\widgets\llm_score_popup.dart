import 'package:flutter/material.dart';

class LlmScorePopup extends StatelessWidget {
  final String? reasonList;
  final double? score;

  const LlmScorePopup({
    super.key,
    required this.reasonList,
    required this.score,
  });

  @override
  Widget build(BuildContext context) {
    if (reasonList == null || reasonList!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 400,
          maxHeight: 500,
        ),
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Positivitätsbewertung',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // Score display
            if (score != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getScoreColor(score!).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _getScoreColor(score!),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${(score! * 100).round()}% positiv',
                  style: TextStyle(
                    color: _getScoreColor(score!),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            
            const SizedBox(height: 16),
            
            // Reasons section
            Text(
              'Begründung:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            
            // Scrollable reasons list
            Flexible(
              child: SingleChildScrollView(
                child: _buildReasonsList(context),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Close button
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Schließen'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReasonsList(BuildContext context) {
    final reasons = reasonList!.split(';')
        .map((reason) => reason.trim())
        .where((reason) => reason.isNotEmpty)
        .toList();

    if (reasons.isEmpty) {
      return Text(
        'Keine Begründung verfügbar.',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontStyle: FontStyle.italic,
          color: Colors.grey[600],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: reasons.asMap().entries.map((entry) {
        final index = entry.key;
        final reason = entry.value;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 6, right: 8),
                width: 6,
                height: 6,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  shape: BoxShape.circle,
                ),
              ),
              Expanded(
                child: Text(
                  reason,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 0.8) {
      return Colors.green;
    } else if (score >= 0.6) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  static void show(BuildContext context, String? reasonList, double? score) {
    if (reasonList == null || reasonList.isEmpty) {
      // Show a simple snackbar if no reasons are available
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Keine Begründung für diese Bewertung verfügbar.'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => LlmScorePopup(
        reasonList: reasonList,
        score: score,
      ),
    );
  }
}
