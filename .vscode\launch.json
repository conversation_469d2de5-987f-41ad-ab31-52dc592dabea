{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "news_aggregator",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/etl/news_aggregator.py",
            "console": "integratedTerminal",
            "env": {
                "FLASK_ENV": "development",
            },
            "justMyCode": false
        },
        {
            "name": "Python Debugger: FastAPI",
            "type": "debugpy",
            "request": "launch",
            "module": "uvicorn",
            "args": [
                "backend.app.main:app",
                "--reload",
                "--host", "0.0.0.0",
                "--workers=1",
                "--reload-dir=backend/app",
                "--log-level=trace"
            ],
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}", 
            "jinja": true,
            "justMyCode": false
        },
        {
            "name": "Flutter: Debug (Local Backend)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define=ENVIRONMENT=development"
            ]
        },
        {
            "name": "Flutter: Release (Local Backend)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--dart-define=ENVIRONMENT=development"
            ]
        },
        {
            "name": "Flutter: Profile (Local Backend)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
            "args": [
                "--dart-define=ENVIRONMENT=development"
            ]
        },
        {
            "name": "Flutter: Debug (Staging Backend)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define=ENVIRONMENT=staging"
            ]
        },
        {
            "name": "Flutter: Debug (Production Backend)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define=ENVIRONMENT=production"
            ]
        },
        {
            "name": "Flutter: Release (Production Backend)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--dart-define=ENVIRONMENT=production"
            ]
        },
        {
            "name": "positive_news_app (legacy)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart"
        }
    ]
}
