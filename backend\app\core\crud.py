from datetime import datetime, timedelta, timezone
from typing import List, Optional, Dict, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, case
from backend.app.core.config import settings
from backend.app.models.models import Entry, Source, Category

print("crud.py loaded - using optimized queries for optimal performance")


def get_news(
    db: Session,
    skip: int = 0,
    limit: int = 10,
    min_positive_score: float = settings.NEWS_SENTIMENT_THRESHOLD,
    include_ads: bool = False,
    hours_back: int = 24,
    category: Optional[str] = None,
    search: Optional[str] = None
) -> Tuple[List[Dict], int]:
    """
    Optimized version that gets the latest news without complex subqueries.
    This is much faster but may include duplicates - we'll handle deduplication in the frontend if needed.
    """
    print(f"get_news called with skip={skip}, limit={limit}, category={category}")
    
    # Time filter: Only news from the last X hours
    time_threshold = datetime.now(timezone.utc) - timedelta(hours=hours_back)

    # Single, simple query - no subqueries at all
    query = (
        db.query(
            Entry.entry_id,
            Entry.iptc_newscode,
            Entry.source,
            Entry.published,
            Entry.llm_positive,
            Entry.llm_reason_list,
            Entry.dup_entry_id,
            Entry.title,
            Entry.link,
            Entry.description,
            Entry.description_auto_generated,
            case((Entry.preview_img.isnot(None), True), else_=False).label("has_image"),
            Category.category.label("category_name"),
            Source.name.label("source_name")
        )
        .join(Category, Entry.iptc_newscode == Category.iptc_newscode, isouter=True)
        .join(Source, Entry.source == Source.source, isouter=True)
        .filter(
            Entry.llm_positive >= min_positive_score,
            Entry.published >= time_threshold,
            Entry.iptc_newscode.isnot(None)
        )
    )

    # Exclude ads if not explicitly included
    if not include_ads:
        query = query.filter(Entry.llm_is_ad < 0.5)

    # Filter by category if specified
    if category:
        query = query.filter(Entry.iptc_newscode == category)
        
    # Add search filter if specified
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Entry.title.ilike(search_term),
                Entry.description.ilike(search_term)
            )
        )

    # Count total results (this is fast with proper indexes)
    total = query.count()
    print(f"Total results found: {total}")

    # Apply pagination and ordering - simple and fast
    entries = query.order_by(Entry.published.desc()).offset(skip).limit(limit).all()
    print(f"Returning {len(entries)} entries")

    # Convert to dictionaries
    result_entries = [
        {k: v for k, v in entry._asdict().items() if not k.startswith('_')}
        for entry in entries
    ]

    return result_entries, total


def get_news_by_category(
    db: Session,
    min_positive_score: float = settings.NEWS_SENTIMENT_THRESHOLD,
    hours_back: int = 24,
    limit_per_category: int = 3
) -> Dict[str, Dict]:
    """
    Optimized version that gets top N news per category without complex subqueries.
    Uses a single query with LIMIT per category using window functions.
    """
    print(f"get_news_by_category called with limit_per_category={limit_per_category}")
    
    # Time filter
    time_threshold = datetime.now(timezone.utc) - timedelta(hours=hours_back)

    # Single query with window function - much simpler than subqueries
    query = (
        db.query(
            Entry.entry_id,
            Entry.iptc_newscode,
            Entry.source,
            Entry.published,
            Entry.llm_positive,
            Entry.llm_reason_list,
            Entry.dup_entry_id,
            Entry.title,
            Entry.link,
            Entry.description,
            Entry.description_auto_generated,
            case((Entry.preview_img.isnot(None), True), else_=False).label("has_image"),
            Category.category.label("category_name"),
            Source.name.label("source_name"),
            func.row_number().over(
                partition_by=Entry.iptc_newscode,
                order_by=Entry.published.desc()
            ).label("row_num")
        )
        .join(Category, Entry.iptc_newscode == Category.iptc_newscode, isouter=True)
        .join(Source, Entry.source == Source.source, isouter=True)
        .filter(
            Entry.llm_positive >= min_positive_score,
            Entry.published >= time_threshold,
            Entry.iptc_newscode.isnot(None),
            Entry.llm_is_ad < 0.5
        )
    ).subquery()

    # Filter to get only top N per category
    final_query = (
        db.query(query)
        .filter(query.c.row_num <= limit_per_category)
        .order_by(query.c.iptc_newscode, query.c.published.desc())
    )

    results = final_query.all()
    print(f"Found {len(results)} results across categories")

    # Group results by category
    grouped_entries = {}
    for result in results:
        category_code = result.iptc_newscode
        if category_code not in grouped_entries:
            grouped_entries[category_code] = {
                "name": result.category_name,
                "entries": []
            }

        # Convert result to dict, excluding row_num
        entry_dict = {k: v for k, v in result._asdict().items() 
                     if not k.startswith('_') and k != 'row_num'}
        grouped_entries[category_code]["entries"].append(entry_dict)

    print(f"Grouped into {len(grouped_entries)} categories")
    return grouped_entries



def check_has_similar_news(db: Session, entry_ids: List[str]) -> Dict[str, bool]:
    """
    Efficiently check which entries have similar news.
    This is called separately when the frontend needs this information.
    """
    print(f"check_has_similar_news called for {len(entry_ids)} entries")
    
    if not entry_ids:
        return {}

    # Simple query to get dup_entry_ids
    dup_mapping = dict(
        db.query(Entry.entry_id, Entry.dup_entry_id)
        .filter(Entry.entry_id.in_(entry_ids))
        .all()
    )

    if not dup_mapping:
        return {entry_id: False for entry_id in entry_ids}

    # Count distinct sources per dup_entry_id
    dup_entry_ids = list(dup_mapping.values())
    similar_counts = dict(
        db.query(
            Entry.dup_entry_id,
            func.count(func.distinct(Entry.source))
        )
        .filter(Entry.dup_entry_id.in_(dup_entry_ids))
        .group_by(Entry.dup_entry_id)
        .all()
    )

    # Map back to entry_ids
    result = {}
    for entry_id in entry_ids:
        dup_entry_id = dup_mapping.get(entry_id)
        if dup_entry_id:
            result[entry_id] = similar_counts.get(dup_entry_id, 0) > 1
        else:
            result[entry_id] = False

    print(f"Similar news check completed: {sum(result.values())} entries have similar news")
    return result


def get_similar_news(db: Session, entry_id: str) -> Tuple[Optional[Dict], List[Dict]]:
    """
    Optimized version of get_similar_news - get all entries with same dup_entry_id.
    """
    print(f"get_similar_news called for entry_id={entry_id}")
    
    # Get the original entry
    entry = db.query(
        Entry.entry_id,
        Entry.iptc_newscode,
        Entry.source,
        Entry.published,
        Entry.llm_positive,
        Entry.llm_reason_list,
        Entry.dup_entry_id,
        Entry.title,
        Entry.link,
        Entry.description,
        Entry.description_auto_generated,
        case((Entry.preview_img.isnot(None), True), else_=False).label("has_image"),
    ).filter(Entry.entry_id == entry_id).first()
    
    if not entry or not entry.dup_entry_id:
        print(f"Entry not found or no dup_entry_id for {entry_id}")
        return None, []

    # Simple query to get similar entries
    similar_query = (
        db.query(
            Entry.entry_id,
            Entry.iptc_newscode,
            Entry.source,
            Entry.published,
            Entry.llm_positive,
            Entry.llm_reason_list,
            Entry.dup_entry_id,
            Entry.title,
            Entry.link,
            Entry.description,
            Entry.description_auto_generated,
            case((Entry.preview_img.isnot(None), True), else_=False).label("has_image"),
            Category.category.label("category_name"),
            Source.name.label("source_name"),
        )
        .join(Category, Entry.iptc_newscode == Category.iptc_newscode, isouter=True)
        .join(Source, Entry.source == Source.source, isouter=True)
        .filter(
            Entry.dup_entry_id == entry.dup_entry_id,
            Entry.entry_id != entry_id,
            Entry.source != entry.source
        )
        .order_by(Entry.published.desc())
    )

    similar_entries = similar_query.all()
    print(f"Found {len(similar_entries)} similar entries")

    # Convert to dictionaries
    original_dict = {k: v for k, v in entry._asdict().items() if not k.startswith('_')}
    similar_entries_list = [
        {k: v for k, v in entry._asdict().items() if not k.startswith('_')}
        for entry in similar_entries
    ]

    return original_dict, similar_entries_list


# Utility functions for sources, categories, and entries
def get_sources(db: Session) -> List[Source]:
    """
    Get all available news sources.
    
    Args:
        db: Database session
        
    Returns:
        List of all Source objects
    """
    return db.query(Source).all()


def get_source_by_id(db: Session, source_id: str) -> Optional[Source]:
    """
    Get a specific news source by ID.
    
    Args:
        db: Database session
        source_id: ID of the source
        
    Returns:
        Source object or None if not found
    """
    return db.query(Source).filter(Source.source == source_id).first()


def get_categories(db: Session) -> List[Category]:
    """
    Get all available categories.
    
    Args:
        db: Database session
        
    Returns:
        List of all Category objects
    """
    return db.query(Category).all()


def get_category_by_id(db: Session, category_id: str) -> Optional[Category]:
    """
    Get a specific category by ID.
    
    Args:
        db: Database session
        category_id: ID of the category (iptc_newscode)
        
    Returns:
        Category object or None if not found
    """
    return db.query(Category).filter(Category.iptc_newscode == category_id).first()


def get_entry_by_id(db: Session, entry_id: str) -> Optional[Entry]:
    """
    Retrieve a news entry by its ID.
    
    Args:
        db: Database session
        entry_id: ID of the entry
        
    Returns:
        Entry object or None if not found
    """
    return db.query(Entry).filter(Entry.entry_id == entry_id).first()