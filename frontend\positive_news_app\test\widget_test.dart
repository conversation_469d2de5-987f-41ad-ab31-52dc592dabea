import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/models/news_model.dart';
import 'package:breakingbright/widgets/category_news_widget.dart';
import 'package:provider/provider.dart';
import 'package:breakingbright/services/favorites_service.dart';
import 'package:breakingbright/widgets/news_card.dart';

void main() {
  group('UI-Tests für NewsCard', () {
    testWidgets('NewsCard zeigt Titel und Quelle korrekt an', (WidgetTester tester) async {
      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.95,
        hasSimilar: false,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChangeNotifierProvider(
              create: (_) => FavoritesService(),
              child: NewsCard(
                news: news,
                showFavoriteButton: true,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Test Nachricht'), findsOneWidget);
      expect(find.text('Test Quelle'), findsOneWidget);
    });

    testWidgets('NewsCard zeigt "Ähnliche Nachrichten verfügbar" an, wenn hasSimilar vorhanden ist', (WidgetTester tester) async {
      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.95,
        hasSimilar: true, // Ensure this is set to true
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChangeNotifierProvider(
              create: (_) => FavoritesService(),
              child: NewsCard(
                news: news,
                showFavoriteButton: true,
              ),
            ),
          ),
        ),
      );

      // Verify that the text is displayed correctly
      expect(find.text('Ähnliche Nachrichten verfügbar'), findsOneWidget);
    });
  });

  group('UI-Tests für CategoryNewsWidget', () {
    testWidgets('CategoryNewsWidget zeigt Kategorienamen und Nachrichten an', 
    (WidgetTester tester) async {
      final news1 = EntryWithSource(
        entryId: 'test_id_1',
        title: 'Test Nachricht 1',
        link: 'https://example.com/1',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.95,
        hasSimilar: false,
      );
      
      final news2 = EntryWithSource(
        entryId: 'test_id_2',
        title: 'Test Nachricht 2',
        link: 'https://example.com/2',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.92,
        hasSimilar: false,
      );

      final categoryNews = CategoryNews(
        category: 'Politik',
        categoryCode: 'POL',
        news: [news1, news2],
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChangeNotifierProvider(
              create: (_) => FavoritesService(),
              child: CategoryNewsWidget(
                categoryNews: categoryNews,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Politik'), findsOneWidget);
      expect(find.text('Test Nachricht 1'), findsOneWidget);
      expect(find.text('Test Nachricht 2'), findsOneWidget);
    });
  });
}
