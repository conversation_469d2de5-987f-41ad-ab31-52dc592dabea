import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/models/news_model.dart';
import 'package:breakingbright/widgets/category_news_widget.dart';
import 'package:breakingbright/widgets/optimized_news_card.dart';
import 'package:provider/provider.dart';
import 'package:breakingbright/services/favorites_service.dart';
import 'package:breakingbright/services/similar_news_service.dart';
import 'package:breakingbright/providers/settings_provider.dart';
import 'package:breakingbright/services/api_service.dart';
import 'package:breakingbright/widgets/news_card.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

@GenerateMocks([ApiService])
import 'service_test.mocks.dart';

void main() {
  group('UI-Tests für NewsCard', () {
    testWidgets('NewsCard zeigt Titel und Quelle korrekt an', (WidgetTester tester) async {
      final mockApiService = MockApiService();
      when(mockApiService.getImageUrl('test_id', hasImage: false))
          .thenReturn('https://picsum.photos/seed/test_id/400/200');

      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.95,
        llmReasonList: 'Test reason',
        hasSimilar: null, // Updated for lazy loading
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiProvider(
              providers: [
                ChangeNotifierProvider(create: (_) => FavoritesService()),
                ChangeNotifierProvider(create: (_) => SettingsProvider()),
                Provider<ApiService>(create: (_) => mockApiService),
              ],
              child: NewsCard(
                news: news,
                showFavoriteButton: true,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Test Nachricht'), findsOneWidget);
      expect(find.text('Test Quelle'), findsOneWidget);
    });

    testWidgets('NewsCard zeigt "Ähnliche Nachrichten verfügbar" an, wenn hasSimilar true ist', (WidgetTester tester) async {
      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.95,
        llmReasonList: 'Test reason 1; Test reason 2',
        hasSimilar: true, // Explicitly set to true
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiProvider(
              providers: [
                ChangeNotifierProvider(create: (_) => FavoritesService()),
                ChangeNotifierProvider(create: (_) => SettingsProvider()),
                Provider(create: (_) => ApiService()),
              ],
              child: NewsCard(
                news: news,
                showFavoriteButton: true,
              ),
            ),
          ),
        ),
      );

      // Verify that the text is displayed correctly
      expect(find.text('Ähnliche Nachrichten verfügbar'), findsOneWidget);
    });

    testWidgets('NewsCard zeigt Ladeindikator für null hasSimilar', (WidgetTester tester) async {
      final mockApiService = MockApiService();
      
      // Mock the API calls
      when(mockApiService.hasSimilarNews('test_id'))
          .thenAnswer((_) async {
        await Future.delayed(const Duration(milliseconds: 100));
        return true;
      });
      
      // Mock the getImageUrl method
      when(mockApiService.getImageUrl('test_id', hasImage: false))
          .thenReturn('https://picsum.photos/seed/test_id/400/200');

      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.95,
        llmReasonList: 'Test reason',
        hasSimilar: null, // Null for lazy loading
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiProvider(
              providers: [
                ChangeNotifierProvider(create: (_) => FavoritesService()),
                ChangeNotifierProvider(create: (_) => SettingsProvider()),
                Provider<ApiService>(create: (_) => mockApiService),
              ],
              child: NewsCard(
                news: news,
                showFavoriteButton: true,
              ),
            ),
          ),
        ),
      );

      // Initially should show loading indicator
      expect(find.text('Prüfe ähnliche Nachrichten...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for the future to complete
      await tester.pumpAndSettle();

      // Should now show the similar news button
      expect(find.text('Ähnliche Nachrichten verfügbar'), findsOneWidget);
      expect(find.text('Prüfe ähnliche Nachrichten...'), findsNothing);
    });
  });

  group('UI-Tests für CategoryNewsWidget', () {
    testWidgets('CategoryNewsWidget zeigt Kategorienamen und Nachrichten an', 
    (WidgetTester tester) async {
      final news1 = EntryWithSource(
        entryId: 'test_id_1',
        title: 'Test Nachricht 1',
        link: 'https://example.com/1',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.95,
        llmReasonList: 'Test reason 1',
        hasSimilar: null, // Updated for lazy loading
      );

      final news2 = EntryWithSource(
        entryId: 'test_id_2',
        title: 'Test Nachricht 2',
        link: 'https://example.com/2',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.92,
        llmReasonList: 'Test reason 2',
        hasSimilar: null, // Updated for lazy loading
      );

      final categoryNews = CategoryNews(
        category: 'Politik',
        categoryCode: 'POL',
        news: [news1, news2],
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiProvider(
              providers: [
                ChangeNotifierProvider(create: (_) => FavoritesService()),
                ChangeNotifierProvider(create: (_) => SettingsProvider()),
                Provider(create: (_) => ApiService()),
              ],
              child: CategoryNewsWidget(
                categoryNews: categoryNews,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Politik'), findsOneWidget);
      expect(find.text('Test Nachricht 1'), findsOneWidget);
      expect(find.text('Test Nachricht 2'), findsOneWidget);
    });
  });

  group('UI-Tests für OptimizedNewsCard', () {
    testWidgets('OptimizedNewsCard zeigt Titel und Quelle korrekt an', (WidgetTester tester) async {
      final mockApiService = MockApiService();
      final similarNewsService = SimilarNewsService(mockApiService);

      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht Optimized',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle Optimized',
        published: DateTime.now(),
        llmPositive: 0.95,
        llmReasonList: 'Test reason',
        hasSimilar: null,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiProvider(
              providers: [
                ChangeNotifierProvider(create: (_) => FavoritesService()),
                ChangeNotifierProvider(create: (_) => SettingsProvider()),
                Provider<ApiService>(create: (_) => mockApiService),
                ChangeNotifierProvider<SimilarNewsService>(create: (_) => similarNewsService),
              ],
              child: OptimizedNewsCard(
                news: news,
                showFavoriteButton: true,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Test Nachricht Optimized'), findsOneWidget);
      expect(find.text('Test Quelle Optimized'), findsOneWidget);
    });

    testWidgets('OptimizedNewsCard verwendet SimilarNewsService für cached Flags', (WidgetTester tester) async {
      final mockApiService = MockApiService();
      final similarNewsService = SimilarNewsService(mockApiService);

      // Pre-cache a similar flag
      when(mockApiService.checkSimilarNews(['test_id']))
          .thenAnswer((_) async => {'test_id': true});
      
      await similarNewsService.loadSimilarFlags(['test_id']);

      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.95,
        hasSimilar: null, // Null, but should use cached value
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiProvider(
              providers: [
                ChangeNotifierProvider(create: (_) => FavoritesService()),
                ChangeNotifierProvider(create: (_) => SettingsProvider()),
                Provider<ApiService>(create: (_) => mockApiService),
                ChangeNotifierProvider<SimilarNewsService>(create: (_) => similarNewsService),
              ],
              child: OptimizedNewsCard(
                news: news,
                showFavoriteButton: true,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show similar news button immediately (no loading)
      expect(find.text('Ähnliche Nachrichten verfügbar'), findsOneWidget);
      expect(find.text('Prüfe ähnliche Nachrichten...'), findsNothing);
    });

    testWidgets('OptimizedNewsCard zeigt Ladeindikator während Service lädt', (WidgetTester tester) async {
      final mockApiService = MockApiService();
      final similarNewsService = SimilarNewsService(mockApiService);

      // Mock delayed response
      when(mockApiService.checkSimilarNews(['test_id']))
          .thenAnswer((_) async {
        await Future.delayed(const Duration(milliseconds: 100));
        return {'test_id': false};
      });

      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        published: DateTime.now(),
        hasSimilar: null,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiProvider(
              providers: [
                ChangeNotifierProvider(create: (_) => FavoritesService()),
                ChangeNotifierProvider(create: (_) => SettingsProvider()),
                Provider<ApiService>(create: (_) => mockApiService),
                ChangeNotifierProvider<SimilarNewsService>(create: (_) => similarNewsService),
              ],
              child: OptimizedNewsCard(
                news: news,
                showFavoriteButton: true,
                showSimilarMessage: true,
              ),
            ),
          ),
        ),
      );

      // Trigger the loading
      await tester.pump();

      // Should show loading initially
      expect(find.text('Prüfe ähnliche Nachrichten...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for completion
      await tester.pumpAndSettle();

      // Should hide the button (no similar news)
      expect(find.text('Prüfe ähnliche Nachrichten...'), findsNothing);
      expect(find.text('Ähnliche Nachrichten verfügbar'), findsNothing);
    });
  });

  group('Lazy Loading Integration Widget Tests', () {
    testWidgets('NewsCard und OptimizedNewsCard verhalten sich konsistent', (WidgetTester tester) async {
      final mockApiService = MockApiService();
      final similarNewsService = SimilarNewsService(mockApiService);

      when(mockApiService.hasSimilarNews('test_id'))
          .thenAnswer((_) async => true);
      when(mockApiService.checkSimilarNews(['test_id']))
          .thenAnswer((_) async => {'test_id': true});

      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        hasSimilar: null,
      );

      // Test NewsCard
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiProvider(
              providers: [
                ChangeNotifierProvider(create: (_) => FavoritesService()),
                ChangeNotifierProvider(create: (_) => SettingsProvider()),
                Provider<ApiService>(create: (_) => mockApiService),
              ],
              child: NewsCard(
                news: news,
                showFavoriteButton: true,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.text('Ähnliche Nachrichten verfügbar'), findsOneWidget);

      // Test OptimizedNewsCard
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiProvider(
              providers: [
                ChangeNotifierProvider(create: (_) => FavoritesService()),
                ChangeNotifierProvider(create: (_) => SettingsProvider()),
                Provider<ApiService>(create: (_) => mockApiService),
                ChangeNotifierProvider<SimilarNewsService>(create: (_) => similarNewsService),
              ],
              child: OptimizedNewsCard(
                news: news,
                showFavoriteButton: true,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.text('Ähnliche Nachrichten verfügbar'), findsOneWidget);
    });
  });
}
