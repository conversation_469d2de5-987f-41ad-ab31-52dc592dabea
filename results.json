{"timestamp": "2025-08-10T21:33:47.484495", "duration_seconds": 8.59, "platform": "Windows", "python_version": "3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]", "summary": {"total_tests": 198, "passed": 142, "failed": 0, "errors": 56, "skipped": 3, "success_rate": 71.7}, "failures": [], "errors": [{"test": "test_multiprocessing_simulation (test_advanced.TestConcurrencyAndThreading.test_multiprocessing_simulation)", "error": "AttributeError: Can't get local object 'TestConcurrencyAndThreading.test_multiprocessing_simulation.<locals>.worker_process'"}, {"test": "test_incomplete_rss_configuration (test_advanced.TestConfigurationValidation.test_incomplete_rss_configuration)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_invalid_database_url (test_advanced.TestConfigurationValidation.test_invalid_database_url)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_invalid_yaml_configuration (test_advanced.TestConfigurationValidation.test_invalid_yaml_configuration)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_missing_rss_sources_file (test_advanced.TestConfigurationValidation.test_missing_rss_sources_file)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_entry_id_generation_consistency (test_advanced.TestEdgeCases.test_entry_id_generation_consistency)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_entry_id_generation_with_unicode_characters (test_advanced.TestEdgeCases.test_entry_id_generation_with_unicode_characters)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_html_cleaning_with_very_large_content (test_advanced.TestEdgeCases.test_html_cleaning_with_very_large_content)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_entry_id_generation_with_missing_fields (test_advanced.TestErrorHandling.test_entry_id_generation_with_missing_fields)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_html_cleaning_with_malformed_html (test_advanced.TestErrorHandling.test_html_cleaning_with_malformed_html)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_session_scope_database_connection_error (test_advanced.TestErrorHandling.test_session_scope_database_connection_error)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_complete_news_processing_workflow (test_comprehensive.TestComprehensiveIntegration.test_complete_news_processing_workflow)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_data_validation_and_sanitization (test_comprehensive.TestComprehensiveIntegration.test_data_validation_and_sanitization)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_error_handling_and_recovery (test_comprehensive.TestComprehensiveIntegration.test_error_handling_and_recovery)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_temporary_file_cleanup (test_database_operations.TestMultiprocessingOperations.test_temporary_file_cleanup)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_llm_api_call_failure (test_external_services.TestLLMIntegration.test_llm_api_call_failure)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_llm_api_call_success (test_external_services.TestLLMIntegration.test_llm_api_call_success)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_llm_timeout_handling (test_external_services.TestLLMIntegration.test_llm_timeout_handling)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_embedding_with_empty_text (test_external_services.TestMLModelIntegration.test_embedding_with_empty_text)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_german_sentiment_model_mock (test_external_services.TestMLModelIntegration.test_german_sentiment_model_mock)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_sentence_transformer_embedding (test_external_services.TestMLModelIntegration.test_sentence_transformer_embedding)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_transformers_pipeline (test_external_services.TestMLModelIntegration.test_transformers_pipeline)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_sentence_transformer_proper_mocking (test_fixes.TestFixedExternalServices.test_sentence_transformer_proper_mocking)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_get_entries_in_process_function (test_integration.TestHelperFunctions.test_get_entries_in_process_function)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_clean_html_content_method (test_integration.TestNewsAggregatorIntegration.test_clean_html_content_method)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_generate_entry_id_method (test_integration.TestNewsAggregatorIntegration.test_generate_entry_id_method)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_load_rss_sources_method (test_integration.TestNewsAggregatorIntegration.test_load_rss_sources_method)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_newsaggregator_init (test_integration.TestNewsAggregatorIntegration.test_newsaggregator_init)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_session_scope_context_manager (test_integration.TestNewsAggregatorIntegration.test_session_scope_context_manager)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_session_scope_with_exception (test_integration.TestNewsAggregatorIntegration.test_session_scope_with_exception)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_multiprocessing_simulation (test_advanced.TestConcurrencyAndThreading.test_multiprocessing_simulation)", "error": "AttributeError: Can't get local object 'TestConcurrencyAndThreading.test_multiprocessing_simulation.<locals>.worker_process'"}, {"test": "test_incomplete_rss_configuration (test_advanced.TestConfigurationValidation.test_incomplete_rss_configuration)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_invalid_database_url (test_advanced.TestConfigurationValidation.test_invalid_database_url)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_invalid_yaml_configuration (test_advanced.TestConfigurationValidation.test_invalid_yaml_configuration)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_missing_rss_sources_file (test_advanced.TestConfigurationValidation.test_missing_rss_sources_file)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_entry_id_generation_consistency (test_advanced.TestEdgeCases.test_entry_id_generation_consistency)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_entry_id_generation_with_unicode_characters (test_advanced.TestEdgeCases.test_entry_id_generation_with_unicode_characters)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_html_cleaning_with_very_large_content (test_advanced.TestEdgeCases.test_html_cleaning_with_very_large_content)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_entry_id_generation_with_missing_fields (test_advanced.TestErrorHandling.test_entry_id_generation_with_missing_fields)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_html_cleaning_with_malformed_html (test_advanced.TestErrorHandling.test_html_cleaning_with_malformed_html)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_session_scope_database_connection_error (test_advanced.TestErrorHandling.test_session_scope_database_connection_error)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_get_entries_in_process_function (test_integration.TestHelperFunctions.test_get_entries_in_process_function)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_llm_api_call_failure (test_external_services.TestLLMIntegration.test_llm_api_call_failure)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_llm_api_call_success (test_external_services.TestLLMIntegration.test_llm_api_call_success)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_llm_timeout_handling (test_external_services.TestLLMIntegration.test_llm_timeout_handling)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_embedding_with_empty_text (test_external_services.TestMLModelIntegration.test_embedding_with_empty_text)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_german_sentiment_model_mock (test_external_services.TestMLModelIntegration.test_german_sentiment_model_mock)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_sentence_transformer_embedding (test_external_services.TestMLModelIntegration.test_sentence_transformer_embedding)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_transformers_pipeline (test_external_services.TestMLModelIntegration.test_transformers_pipeline)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_temporary_file_cleanup (test_database_operations.TestMultiprocessingOperations.test_temporary_file_cleanup)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_clean_html_content_method (test_integration.TestNewsAggregatorIntegration.test_clean_html_content_method)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_generate_entry_id_method (test_integration.TestNewsAggregatorIntegration.test_generate_entry_id_method)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_load_rss_sources_method (test_integration.TestNewsAggregatorIntegration.test_load_rss_sources_method)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_newsaggregator_init (test_integration.TestNewsAggregatorIntegration.test_newsaggregator_init)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_session_scope_context_manager (test_integration.TestNewsAggregatorIntegration.test_session_scope_context_manager)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}, {"test": "test_session_scope_with_exception (test_integration.TestNewsAggregatorIntegration.test_session_scope_with_exception)", "error": "cannot import name 'EncoderDecoderConfig' from 'transformers.models.encoder_decoder' (C:\\Dokumente\\Private\\Coding\\goodNews\\.venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\__init__.py)"}]}