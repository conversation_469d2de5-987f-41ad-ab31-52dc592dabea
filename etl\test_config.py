"""
Test configuration and utilities
Provides configuration settings and utilities for the test suite
"""

import os
import platform
import sys
from unittest import TestCase


class TestConfig:
    """Configuration settings for tests"""
    
    # Platform detection
    IS_WINDOWS = platform.system() == 'Windows'
    IS_UNIX = platform.system() in ['Linux', 'Darwin']
    
    # Test environment settings
    SKIP_SLOW_TESTS = os.getenv('SKIP_SLOW_TESTS', 'false').lower() == 'true'
    SKIP_EXTERNAL_TESTS = os.getenv('SKIP_EXTERNAL_TESTS', 'false').lower() == 'true'
    SKIP_MULTIPROCESSING_TESTS = os.getenv('SKIP_MULTIPROCESSING_TESTS', 'false').lower() == 'true'
    
    # Test data settings
    TEST_DATA_DIR = os.path.join(os.path.dirname(__file__), 'test_data')
    TEMP_DIR = os.getenv('TEST_TEMP_DIR', '/tmp' if IS_UNIX else 'c:/temp')
    
    # Mock settings
    ENABLE_REAL_EXTERNAL_CALLS = os.getenv('ENABLE_REAL_EXTERNAL_CALLS', 'false').lower() == 'true'
    
    # Database settings for tests
    TEST_DATABASE_URL = os.getenv('TEST_DATABASE_URL', 'sqlite:///:memory:')
    
    @classmethod
    def should_skip_multiprocessing(cls):
        """Determine if multiprocessing tests should be skipped"""
        return cls.IS_WINDOWS or cls.SKIP_MULTIPROCESSING_TESTS
    
    @classmethod
    def should_skip_external_services(cls):
        """Determine if external service tests should be skipped"""
        return cls.SKIP_EXTERNAL_TESTS
    
    @classmethod
    def get_temp_dir(cls):
        """Get appropriate temporary directory for the platform"""
        temp_dir = cls.TEMP_DIR
        os.makedirs(temp_dir, exist_ok=True)
        return temp_dir


class BaseTestCase(TestCase):
    """Base test case with common utilities"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = TestConfig()
        super().setUp()
    
    def skipIfWindows(self, reason="Test not supported on Windows"):
        """Skip test if running on Windows"""
        if TestConfig.IS_WINDOWS:
            self.skipTest(reason)
    
    def skipIfUnix(self, reason="Test not supported on Unix"):
        """Skip test if running on Unix"""
        if TestConfig.IS_UNIX:
            self.skipTest(reason)
    
    def skipIfSlowTests(self, reason="Slow test skipped"):
        """Skip test if slow tests are disabled"""
        if TestConfig.SKIP_SLOW_TESTS:
            self.skipTest(reason)
    
    def skipIfExternalTests(self, reason="External test skipped"):
        """Skip test if external tests are disabled"""
        if TestConfig.SKIP_EXTERNAL_TESTS:
            self.skipTest(reason)
    
    def assertMockCalled(self, mock_obj, message=None):
        """Assert that a mock was called at least once"""
        if not mock_obj.called:
            msg = message or f"Expected {mock_obj} to be called"
            self.fail(msg)
    
    def assertMockNotCalled(self, mock_obj, message=None):
        """Assert that a mock was not called"""
        if mock_obj.called:
            msg = message or f"Expected {mock_obj} not to be called"
            self.fail(msg)


def skip_on_windows(reason="Test not supported on Windows"):
    """Decorator to skip tests on Windows"""
    import unittest
    return unittest.skipIf(TestConfig.IS_WINDOWS, reason)


def skip_on_unix(reason="Test not supported on Unix"):
    """Decorator to skip tests on Unix"""
    import unittest
    return unittest.skipIf(TestConfig.IS_UNIX, reason)


def skip_if_slow(reason="Slow test skipped"):
    """Decorator to skip slow tests"""
    import unittest
    return unittest.skipIf(TestConfig.SKIP_SLOW_TESTS, reason)


def skip_if_external(reason="External test skipped"):
    """Decorator to skip external service tests"""
    import unittest
    return unittest.skipIf(TestConfig.SKIP_EXTERNAL_TESTS, reason)


def requires_import(module_name):
    """Decorator to skip tests if a module is not available"""
    import unittest
    try:
        __import__(module_name)
        return lambda func: func
    except ImportError:
        return unittest.skip(f"Requires {module_name}")


class MockHelper:
    """Helper class for creating and managing mocks"""
    
    @staticmethod
    def create_mock_entry(title="Test Title", link="https://example.com/test", **kwargs):
        """Create a mock entry with default values"""
        from unittest.mock import Mock
        
        mock_entry = Mock()
        mock_entry.title = title
        mock_entry.link = link
        
        # Add any additional attributes
        for key, value in kwargs.items():
            setattr(mock_entry, key, value)
        
        return mock_entry
    
    @staticmethod
    def create_mock_feed(entries=None, bozo=False):
        """Create a mock RSS feed"""
        from unittest.mock import Mock
        
        mock_feed = Mock()
        mock_feed.bozo = bozo
        mock_feed.entries = entries or []
        
        return mock_feed
    
    @staticmethod
    def create_mock_session():
        """Create a mock database session"""
        from unittest.mock import Mock
        
        mock_session = Mock()
        mock_session.commit = Mock()
        mock_session.rollback = Mock()
        mock_session.close = Mock()
        mock_session.query = Mock()
        
        return mock_session
    
    @staticmethod
    def create_mock_context_manager(return_value=None):
        """Create a mock context manager"""
        from unittest.mock import Mock
        
        mock_cm = Mock()
        mock_cm.__enter__ = Mock(return_value=return_value or mock_cm)
        mock_cm.__exit__ = Mock(return_value=None)
        
        return mock_cm


class TestDataHelper:
    """Helper class for managing test data"""
    
    @staticmethod
    def get_sample_rss_data():
        """Get sample RSS feed data"""
        return {
            'title': 'Sample News Article',
            'link': 'https://example.com/news/sample',
            'description': '<p>This is a sample news article description.</p>',
            'published': '2024-01-01T12:00:00Z'
        }
    
    @staticmethod
    def get_sample_entries(count=3):
        """Get a list of sample entries"""
        entries = []
        for i in range(count):
            entries.append({
                'entry_id': f'test_entry_{i}',
                'title': f'Test Article {i+1}',
                'link': f'https://example.com/article{i+1}',
                'description': f'Description for article {i+1}',
                'published': f'2024-01-0{i+1}T12:00:00Z'
            })
        return entries
    
    @staticmethod
    def get_sample_html_content():
        """Get sample HTML content for testing"""
        return """
        <html>
            <head><title>Test Article</title></head>
            <body>
                <h1>Test Article Title</h1>
                <p>This is the first paragraph with <b>bold</b> text.</p>
                <div>
                    <p>This is a paragraph in a div.</p>
                    <br>
                    <span>Some span content</span>
                </div>
            </body>
        </html>
        """
    
    @staticmethod
    def get_malformed_html_content():
        """Get malformed HTML content for testing"""
        return """
        <html>
            <head><title>Malformed Article
            <body>
                <h1>Missing closing tag
                <p>Unclosed paragraph
                <div>Nested without proper closing
                    <span>More nesting
        """


class AssertionHelper:
    """Helper class for custom assertions"""
    
    @staticmethod
    def assert_dict_contains_keys(test_case, dictionary, keys):
        """Assert that dictionary contains all specified keys"""
        missing_keys = [key for key in keys if key not in dictionary]
        if missing_keys:
            test_case.fail(f"Dictionary missing keys: {missing_keys}")
    
    @staticmethod
    def assert_mock_called_with_partial(test_case, mock_obj, **kwargs):
        """Assert that mock was called with at least the specified arguments"""
        if not mock_obj.called:
            test_case.fail(f"Expected {mock_obj} to be called")
        
        # Check if any call matches the partial arguments
        for call in mock_obj.call_args_list:
            call_kwargs = call.kwargs if hasattr(call, 'kwargs') else {}
            if all(call_kwargs.get(k) == v for k, v in kwargs.items()):
                return
        
        test_case.fail(f"Expected {mock_obj} to be called with {kwargs}")
    
    @staticmethod
    def assert_string_contains_all(test_case, string, substrings):
        """Assert that string contains all specified substrings"""
        missing = [sub for sub in substrings if sub not in string]
        if missing:
            test_case.fail(f"String missing substrings: {missing}")


# Environment setup for tests
def setup_test_environment():
    """Set up the test environment"""
    # Ensure test directories exist
    os.makedirs(TestConfig.TEST_DATA_DIR, exist_ok=True)
    os.makedirs(TestConfig.get_temp_dir(), exist_ok=True)
    
    # Set up logging for tests
    import logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise during tests
    
    # Add project paths
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    if project_root not in sys.path:
        sys.path.append(project_root)
    
    etl_path = os.path.dirname(__file__)
    if etl_path not in sys.path:
        sys.path.append(etl_path)


# Call setup when module is imported
setup_test_environment()