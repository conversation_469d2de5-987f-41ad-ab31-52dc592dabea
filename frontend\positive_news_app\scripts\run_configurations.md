# Flutter Run Configurations

## Available Launch Configurations

### VS Code Launch Configurations
Use the VS Code debugger with these configurations (available in the global `.vscode/launch.json`):

1. **Flutter: Debug (Local Backend)** - Debug mode with local backend
2. **Flutter: Release (Local Backend)** - Release mode with local backend  
3. **Flutter: Profile (Local Backend)** - Profile mode with local backend
4. **Flutter: Debug (Staging Backend)** - Debug mode with staging backend
5. **Flutter: Debug (Production Backend)** - Debug mode with production backend
6. **Flutter: Release (Production Backend)** - Release mode with production backend
7. **positive_news_app (legacy)** - Default debug mode (for backward compatibility)

### Command Line Usage

#### Local Development (Default)
```bash
# Debug mode with local backend (default)
flutter run

# Release mode with local backend
flutter run --release --dart-define=ENVIRONMENT=development

# Profile mode with local backend
flutter run --profile --dart-define=ENVIRONMENT=development
```

#### Staging Environment
```bash
# Debug mode with staging backend
flutter run --dart-define=ENVIRONMENT=staging

# Release mode with staging backend
flutter run --release --dart-define=ENVIRONMENT=staging
```

#### Production Environment
```bash
# Debug mode with production backend
flutter run --dart-define=ENVIRONMENT=production

# Release mode with production backend
flutter run --release --dart-define=ENVIRONMENT=production
```

## Environment Configuration

### Backend URLs
- **Development**: `http://192.168.3.123:8000` (your local backend)
- **Staging**: `https://api.breakingbright.de` (production backend with debug logging)
- **Production**: `https://api.breakingbright.de` (production backend, no debug logging)

### Environment Detection Logic
1. **Environment Variable**: Checks `ENVIRONMENT` dart-define first
2. **Flutter Mode**: Falls back to Flutter's debug/profile/release mode
3. **Default**: Always defaults to development (local backend) unless explicitly overridden

### Key Benefits
- ✅ **Safe Default**: Always uses local backend unless explicitly told otherwise
- ✅ **Explicit Control**: Use environment variables to override behavior
- ✅ **Multiple Modes**: Can run release mode locally with local backend
- ✅ **Easy Testing**: Can test against production backend in debug mode

## Examples

### Testing Release Build Locally
```bash
# This will use your local backend even in release mode
flutter run --release --dart-define=ENVIRONMENT=development
```

### Testing Against Production Backend
```bash
# This will use production backend in debug mode (for easier debugging)
flutter run --dart-define=ENVIRONMENT=production
```

### Building for Production
```bash
# Build for production with production backend
flutter build apk --release --dart-define=ENVIRONMENT=production
flutter build ios --release --dart-define=ENVIRONMENT=production
```