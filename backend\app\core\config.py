from pydantic_settings import BaseSettings
from typing import List


class Settings(BaseSettings):
    # App settings
    APP_NAME: str = "Positive News API"
    APP_VERSION: str = "0.1.0"
    APP_DESCRIPTION: str = "API für eine plattformübergreifende Nachrichten-App mit ausschließlich positiven Nachrichten"

    # Database settings
    DATABASE_URL: str

    # API settings
    API_PREFIX: str = ""
    API_CORS_ORIGINS: List[str] = ["*"]

    # API keys for LLM providers
    IONOS_API_KEY: str = ""
    GROQ_API_KEY: str = ""
    OPENROUTER_API_KEY: str = ""
    CHUTES_API_KEY: str = ""
    GOOGLE_API_KEY: str = ""
    TOGETHER_API_KEY: str = ""

    # Interval settings
    INTERVAL_FEEDS: int = 900
    INTERVAL_FULL_TEXTS: int = 30
    INTERVAL_SENTIMENT: int = 30
    INTERVAL_LLM: int = 30
    INTERVAL_RSS_FEED: int = 3600
    INTERVAL_IPTC_CLASSIFICATION: int = 30
    INTERVAL_TRANSLATION: int = 30
    INTERVAL_EMBEDDING: int = 30
    INTERVAL_DUPLICATE_CHECK: int = 30
    INTERVAL_IMAGE_PROMPT_GENERATION: int = 30
    INTERVAL_PREVIEW_IMAGE: int = 30
    INTERVAL_DESCRIPTION_GENERATION: int = 30
    
    # News settings
    NEWS_SIMILARITY_THRESHOLD: float = 0.8
    NEWS_SENTIMENT_THRESHOLD: float = 0.7
    NEWS_LOOKBACK_DAYS: int = 2
    NEWS_DUPLICATE_LOOKBACK_DAYS: int = 5
    NEWS_LLM_MAX_INPUT_LENGTH: int = 15000
    NEWS_RSS_SOURCES_FILE: str = "news_sources.yaml"

    # Image prompt settings
    IMAGE_PROMPT_MIN_TOKENS: int = 50
    IMAGE_PROMPT_MAX_TOKENS: int = 200

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Create settings instance
settings = Settings()

# Debug print to verify configuration (remove in production)
print(f"Database URL: {settings.DATABASE_URL}")
