"""
Tests for external service integrations
Covers RSS feeds, translation services, ML models, and LLM integration with comprehensive mocking
"""

import unittest
from unittest.mock import Mock, patch, MagicMock, call
import os
import sys
import json
import numpy as np
from datetime import datetime, timezone

# Add paths for imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
etl_path = os.path.dirname(__file__)
sys.path.append(etl_path)


class TestRSSFeedProcessing(unittest.TestCase):
    """Test RSS feed processing with various scenarios"""

    def test_successful_rss_parsing(self):
        """Test successful RSS feed parsing"""
        with patch('feedparser.parse') as mock_parse:
            # Mock successful feed
            mock_feed = Mock()
            mock_feed.bozo = False
            mock_feed.entries = []
            
            # Create mock entries
            for i in range(3):
                mock_entry = Mock()
                mock_entry.title = f"Article {i+1}"
                mock_entry.link = f"https://example.com/article{i+1}"
                mock_entry.description = f"<p>Description {i+1}</p>"
                mock_entry.published_parsed = (2024, 1, i+1, 12, 0, 0, 0, 1, -1)
                mock_feed.entries.append(mock_entry)
            
            mock_parse.return_value = mock_feed
            
            # Test parsing
            import feedparser
            feed = feedparser.parse("https://example.com/rss")
            
            self.assertFalse(feed.bozo)
            self.assertEqual(len(feed.entries), 3)
            self.assertEqual(feed.entries[0].title, "Article 1")

    def test_malformed_rss_feed(self):
        """Test handling of malformed RSS feeds"""
        with patch('feedparser.parse') as mock_parse:
            # Mock malformed feed
            mock_feed = Mock()
            mock_feed.bozo = True
            mock_feed.bozo_exception = Exception("Feed is not well-formed")
            mock_feed.entries = []
            
            mock_parse.return_value = mock_feed
            
            # Test parsing
            import feedparser
            feed = feedparser.parse("https://example.com/bad-rss")
            
            self.assertTrue(feed.bozo)
            self.assertIsNotNone(feed.bozo_exception)
            self.assertEqual(len(feed.entries), 0)

    def test_rss_feed_with_missing_fields(self):
        """Test RSS feed entries with missing fields"""
        with patch('feedparser.parse') as mock_parse:
            # Mock feed with incomplete entries
            mock_feed = Mock()
            mock_feed.bozo = False
            
            # Entry with missing description
            mock_entry1 = Mock()
            mock_entry1.title = "Article 1"
            mock_entry1.link = "https://example.com/article1"
            # Simulate missing description by not setting it
            del mock_entry1.description  # Remove description attribute
            mock_entry1.published_parsed = (2024, 1, 1, 12, 0, 0, 0, 1, -1)
            
            # Entry with missing published date
            mock_entry2 = Mock()
            mock_entry2.title = "Article 2"
            mock_entry2.link = "https://example.com/article2"
            mock_entry2.description = "Description 2"
            # Simulate missing published_parsed by not setting it
            del mock_entry2.published_parsed
            
            mock_feed.entries = [mock_entry1, mock_entry2]
            mock_parse.return_value = mock_feed
            
            # Test parsing
            import feedparser
            feed = feedparser.parse("https://example.com/incomplete-rss")
            
            self.assertEqual(len(feed.entries), 2)
            # Should handle missing attributes gracefully
            self.assertTrue(hasattr(feed.entries[0], 'title'))
            self.assertFalse(hasattr(feed.entries[0], 'description'))

    def test_rss_feed_timeout_simulation(self):
        """Test RSS feed timeout handling"""
        with patch('feedparser.parse') as mock_parse:
            # Mock timeout exception
            mock_parse.side_effect = Exception("Connection timeout")
            
            import feedparser
            with self.assertRaises(Exception):
                feedparser.parse("https://slow-example.com/rss")


class TestTranslationService(unittest.TestCase):
    """Test translation service integration"""

    def test_successful_translation(self):
        """Test successful text translation"""
        with patch('deep_translator.GoogleTranslator') as mock_translator_class:
            # Mock translator
            mock_translator = Mock()
            mock_translator.translate.return_value = "This is translated text"
            mock_translator_class.return_value = mock_translator
            
            # Test translation
            from deep_translator import GoogleTranslator
            translator = GoogleTranslator(source='de', target='en')
            result = translator.translate("Das ist ein deutscher Text")
            
            self.assertEqual(result, "This is translated text")
            mock_translator.translate.assert_called_once_with("Das ist ein deutscher Text")

    def test_translation_not_found_error(self):
        """Test handling of translation not found errors"""
        with patch('deep_translator.GoogleTranslator') as mock_translator_class:
            # Mock translator with error
            mock_translator = Mock()
            mock_translator.translate.side_effect = Exception("TranslationNotFound")
            mock_translator_class.return_value = mock_translator
            
            # Test error handling
            from deep_translator import GoogleTranslator
            translator = GoogleTranslator(source='de', target='en')
            
            with self.assertRaises(Exception):
                translator.translate("Untranslatable text")

    def test_translation_with_long_text(self):
        """Test translation with text length limits"""
        with patch('deep_translator.GoogleTranslator') as mock_translator_class:
            # Mock translator
            mock_translator = Mock()
            mock_translator.translate.return_value = "Translated long text"
            mock_translator_class.return_value = mock_translator
            
            # Test with long text (should be truncated)
            from deep_translator import GoogleTranslator
            translator = GoogleTranslator(source='de', target='en')
            
            long_text = "Very long German text " * 1000  # Very long text
            truncated_text = long_text[:4999]  # Simulate truncation
            
            result = translator.translate(truncated_text)
            
            self.assertEqual(result, "Translated long text")
            # Verify truncation happened
            self.assertLessEqual(len(truncated_text), 4999)

    def test_translation_with_special_characters(self):
        """Test translation with special characters and emojis"""
        with patch('deep_translator.GoogleTranslator') as mock_translator_class:
            # Mock translator
            mock_translator = Mock()
            mock_translator.translate.return_value = "Translated text with emojis 🚀"
            mock_translator_class.return_value = mock_translator
            
            # Test with special characters
            from deep_translator import GoogleTranslator
            translator = GoogleTranslator(source='de', target='en')
            
            special_text = "Deutscher Text mit Umlauten äöü und Emojis 🚀🎉"
            result = translator.translate(special_text)
            
            self.assertIn("🚀", result)
            mock_translator.translate.assert_called_once_with(special_text)


class TestMLModelIntegration(unittest.TestCase):
    """Test machine learning model integrations"""

    def test_sentence_transformer_embedding(self):
        """Test sentence transformer embedding generation"""
        with patch('sentence_transformers.SentenceTransformer') as mock_transformer_class:
            # Mock transformer
            mock_transformer = Mock()
            mock_embedding = np.array([0.1, 0.2, 0.3, 0.4, 0.5])
            mock_tensor = Mock()
            mock_tensor.cpu.return_value.numpy.return_value = [mock_embedding]
            mock_transformer.encode.return_value = mock_tensor
            mock_transformer_class.return_value = mock_transformer
            
            # Test embedding generation
            from sentence_transformers import SentenceTransformer
            model = SentenceTransformer('BAAI/bge-m3')
            
            text = "This is test text for embedding"
            embedding = model.encode([text], convert_to_tensor=True).cpu().numpy()[0]
            
            np.testing.assert_array_equal(embedding, mock_embedding)
            mock_transformer.encode.assert_called_once_with([text], convert_to_tensor=True)

    def test_embedding_with_empty_text(self):
        """Test embedding generation with empty text"""
        with patch('sentence_transformers.SentenceTransformer') as mock_transformer_class:
            # Mock transformer
            mock_transformer = Mock()
            mock_embedding = np.array([0.0, 0.0, 0.0, 0.0, 0.0])
            mock_tensor = Mock()
            mock_tensor.cpu.return_value.numpy.return_value = [mock_embedding]
            mock_transformer.encode.return_value = mock_tensor
            mock_transformer_class.return_value = mock_transformer
            
            # Test with empty text
            from sentence_transformers import SentenceTransformer
            model = SentenceTransformer('BAAI/bge-m3')
            
            embedding = model.encode([""], convert_to_tensor=True).cpu().numpy()[0]
            
            # Should handle empty text gracefully
            self.assertEqual(len(embedding), 5)
            mock_transformer.encode.assert_called_once()

    def test_transformers_pipeline(self):
        """Test transformers pipeline for sentiment analysis"""
        with patch('transformers.pipeline') as mock_pipeline:
            # Mock pipeline
            mock_classifier = Mock()
            mock_classifier.return_value = [{'label': 'POSITIVE', 'score': 0.95}]
            mock_pipeline.return_value = mock_classifier
            
            # Test pipeline usage
            from transformers import pipeline
            classifier = pipeline("sentiment-analysis")
            
            result = classifier("This is a great article!")
            
            self.assertEqual(result[0]['label'], 'POSITIVE')
            self.assertEqual(result[0]['score'], 0.95)

    def test_german_sentiment_model_mock(self):
        """Test German sentiment model integration"""
        # Mock the GermanSentiment model
        with patch('germansentiment.SentimentModel') as mock_model_class:
            mock_model = Mock()
            mock_model.predict_sentiment.return_value = (
                ['positive'], 
                [[('positive', 0.8), ('neutral', 0.15), ('negative', 0.05)]]
            )
            mock_model_class.return_value = mock_model
            
            # Simulate usage
            from germansentiment import SentimentModel
            model = SentimentModel()
            
            predictions, probabilities = model.predict_sentiment(
                ["Das ist ein großartiger Artikel!"], 
                output_probabilities=True
            )
            
            self.assertEqual(predictions[0], 'positive')
            self.assertEqual(len(probabilities[0]), 3)  # positive, neutral, negative

    def test_vader_sentiment_analyzer_mock(self):
        """Test VADER sentiment analyzer integration"""
        with patch('nltk.sentiment.vader.SentimentIntensityAnalyzer') as mock_analyzer_class:
            mock_analyzer = Mock()
            mock_analyzer.polarity_scores.return_value = {
                'pos': 0.8,
                'neu': 0.15,
                'neg': 0.05,
                'compound': 0.75
            }
            mock_analyzer_class.return_value = mock_analyzer
            
            # Simulate usage
            from nltk.sentiment.vader import SentimentIntensityAnalyzer
            analyzer = SentimentIntensityAnalyzer()
            
            scores = analyzer.polarity_scores("This is a great article!")
            
            self.assertEqual(scores['pos'], 0.8)
            self.assertEqual(scores['compound'], 0.75)


class TestLLMIntegration(unittest.TestCase):
    """Test LLM integration and API calls"""

    def test_llm_request_structure(self):
        """Test LLM request structure and validation"""
        # Test request data structure
        llm_request = {
            "model": "qwen2.5-7b-instruct",
            "messages": [
                {"role": "user", "content": "Analyze this text for sentiment"}
            ],
            "temperature": 0.1,
            "max_tokens": 800,
            "response_format": {"type": "json_object"}
        }
        
        # Validate structure
        self.assertIn("model", llm_request)
        self.assertIn("messages", llm_request)
        self.assertIsInstance(llm_request["messages"], list)
        self.assertEqual(llm_request["messages"][0]["role"], "user")

    @patch('news_aggregator.requests.post')
    def test_llm_api_call_success(self, mock_post):
        """Test successful LLM API call"""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "choices": [{
                "message": {
                    "content": json.dumps({
                        "advertisement": {
                            "is_advertisement": 0.1,
                            "reason": "This appears to be news content"
                        },
                        "sentiment": {
                            "positive": 0.8,
                            "neutral": 0.15,
                            "negative": 0.05,
                            "reason": "Positive sentiment detected"
                        }
                    })
                }
            }]
        }
        mock_post.return_value = mock_response
        
        # Test API call
        import requests
        response = requests.post(
            "http://localhost:1234/v1/chat/completions",
            json={"model": "test", "messages": [{"role": "user", "content": "test"}]}
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        content = json.loads(data["choices"][0]["message"]["content"])
        self.assertIn("advertisement", content)
        self.assertIn("sentiment", content)

    @patch('news_aggregator.requests.post')
    def test_llm_api_call_failure(self, mock_post):
        """Test LLM API call failure handling"""
        # Mock failed response
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_post.return_value = mock_response
        
        # Test API call
        import requests
        response = requests.post(
            "http://localhost:1234/v1/chat/completions",
            json={"model": "test", "messages": [{"role": "user", "content": "test"}]}
        )
        
        self.assertEqual(response.status_code, 500)

    @patch('news_aggregator.requests.post')
    def test_llm_timeout_handling(self, mock_post):
        """Test LLM API timeout handling"""
        # Mock timeout
        mock_post.side_effect = Exception("Connection timeout")
        
        # Test timeout handling
        import requests
        with self.assertRaises(Exception):
            requests.post(
                "http://localhost:1234/v1/chat/completions",
                json={"model": "test", "messages": [{"role": "user", "content": "test"}]},
                timeout=30
            )

    def test_llm_response_validation(self):
        """Test LLM response validation against schema"""
        # Test valid response
        valid_response = {
            "advertisement": {
                "is_advertisement": 0.2,
                "reason": "Contains some promotional language"
            },
            "sentiment": {
                "positive": 0.7,
                "neutral": 0.2,
                "negative": 0.1,
                "reason": "Overall positive tone"
            }
        }
        
        # Validate required fields
        self.assertIn("advertisement", valid_response)
        self.assertIn("sentiment", valid_response)
        self.assertIn("is_advertisement", valid_response["advertisement"])
        self.assertIn("reason", valid_response["advertisement"])
        
        # Validate sentiment probabilities sum to 1.0 (approximately)
        sentiment = valid_response["sentiment"]
        total = sentiment["positive"] + sentiment["neutral"] + sentiment["negative"]
        self.assertAlmostEqual(total, 1.0, places=1)

    def test_llm_input_text_truncation(self):
        """Test LLM input text truncation"""
        # Test text truncation logic
        max_length = 5000
        long_text = "This is a very long article " * 200  # Much longer than max_length
        
        truncated = long_text[:max_length]
        
        self.assertLessEqual(len(truncated), max_length)
        self.assertIn("This is a very long article", truncated)

    def test_llm_multiple_iterations(self):
        """Test LLM multiple iterations for consistency"""
        # Simulate multiple LLM calls for consistency checking
        mock_responses = [
            {"positive": 0.8, "neutral": 0.15, "negative": 0.05},
            {"positive": 0.75, "neutral": 0.2, "negative": 0.05},
            {"positive": 0.82, "neutral": 0.13, "negative": 0.05},
            {"positive": 0.78, "neutral": 0.17, "negative": 0.05},
            {"positive": 0.79, "neutral": 0.16, "negative": 0.05}
        ]
        
        # Calculate statistics
        positive_scores = [r["positive"] for r in mock_responses]
        mean_positive = sum(positive_scores) / len(positive_scores)
        std_positive = np.std(positive_scores)
        
        self.assertAlmostEqual(mean_positive, 0.788, places=2)
        self.assertLess(std_positive, 0.05)  # Low standard deviation indicates consistency


class TestWebScrapingIntegration(unittest.TestCase):
    """Test web scraping integration with newspaper3k"""

    def test_successful_article_extraction(self):
        """Test successful article text extraction"""
        with patch('newspaper.Article') as mock_article_class:
            # Mock article
            mock_article = Mock()
            mock_article.text = "This is the full article text content."
            mock_article.title = "Article Title"
            mock_article.authors = ["Author Name"]
            mock_article_class.return_value = mock_article
            
            # Test article extraction
            import newspaper
            article = newspaper.Article("https://example.com/article")
            article.download()
            article.parse()
            
            self.assertEqual(article.text, "This is the full article text content.")
            mock_article.download.assert_called_once()
            mock_article.parse.assert_called_once()

    def test_article_extraction_failure(self):
        """Test article extraction failure handling"""
        with patch('newspaper.Article') as mock_article_class:
            # Mock article with exception
            mock_article = Mock()
            mock_article.download.side_effect = Exception("Download failed")
            mock_article_class.return_value = mock_article
            
            # Test error handling
            import newspaper
            article = newspaper.Article("https://example.com/broken-article")
            
            with self.assertRaises(Exception):
                article.download()

    def test_article_with_no_text(self):
        """Test article with no extractable text"""
        with patch('newspaper.Article') as mock_article_class:
            # Mock article with empty text
            mock_article = Mock()
            mock_article.text = ""
            mock_article_class.return_value = mock_article
            
            # Test handling of empty text
            import newspaper
            article = newspaper.Article("https://example.com/empty-article")
            article.download()
            article.parse()
            
            # Should handle empty text gracefully
            text_result = article.text if article.text else "<n/a>"
            self.assertEqual(text_result, "<n/a>")

    def test_article_timeout_handling(self):
        """Test article download timeout handling"""
        with patch('newspaper.Article') as mock_article_class:
            # Mock article with timeout
            mock_article = Mock()
            mock_article.download.side_effect = Exception("Timeout")
            mock_article_class.return_value = mock_article
            
            # Test timeout handling with ThreadPoolExecutor simulation
            from concurrent.futures import ThreadPoolExecutor, TimeoutError
            
            def fetch_article_with_timeout():
                import newspaper
                article = newspaper.Article("https://slow-example.com/article")
                article.download()
                return article.text
            
            with ThreadPoolExecutor() as executor:
                future = executor.submit(fetch_article_with_timeout)
                try:
                    # This should raise an exception due to our mock
                    result = future.result(timeout=1)
                except Exception as e:
                    # Expected due to our mock
                    self.assertIsInstance(e, Exception)


if __name__ == '__main__':
    unittest.main(verbosity=2)