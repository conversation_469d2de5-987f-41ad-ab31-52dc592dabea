from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from backend.app.core.config import settings

# Create SQLAlchemy engine using the DATABASE_URL from settings
# Keep echo=True for debugging as requested
engine = create_engine(
    settings.DATABASE_URL, 
    echo=True,  # Keep verbose logging as requested
    pool_size=10,  # Connection pool size
    max_overflow=20,  # Additional connections beyond pool_size
    pool_pre_ping=True,  # Validate connections before use
    pool_recycle=3600  # Recycle connections every hour
)
        
# Create sessionmaker
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()


def get_db():
    """
    Dependency for FastAPI to provide a database connection.
    Ensures the connection is closed after use.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
