import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:breakingbright/services/favorites_service.dart';
import 'package:breakingbright/services/api_service.dart';
import 'package:breakingbright/services/connection_status_provider.dart';
import 'package:breakingbright/app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized(); // Ensure Flutter binding is initialized
  final apiService = ApiService();
  
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => FavoritesService()),
        ChangeNotifierProvider(create: (context) => ConnectionStatusProvider(apiService)),
        Provider.value(value: apiService),
      ],
      child: const PositiveNewsApp(),
    ),
  );
}
