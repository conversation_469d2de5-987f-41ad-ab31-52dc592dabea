from typing import List, Dict, Optional
from pydantic import BaseModel
from datetime import datetime


class SourceBase(BaseModel):
    source: str
    name: str


class Source(SourceBase):
    class Config:
        from_attributes = True

        
class CategoryBase(BaseModel):
    iptc_newscode: str
    category: str


class Category(CategoryBase):
    class Config:
        from_attributes = True


class EntryBase(BaseModel):
    entry_id: str
    title: str
    link: str
    source: str
    description: Optional[str] = None
    published: datetime
    full_text: Optional[str] = None
    iptc_newscode: Optional[str] = None
    llm_positive: Optional[float] = None
    llm_reason_list: Optional[str] = None
    dup_entry_id: Optional[str] = None
    description_auto_generated: Optional[bool] = None
    preview_img: Optional[bytes] = None


class Entry(EntryBase):
    class Config:
        from_attributes = True


class EntryWithSource(Entry):
    source_name: Optional[str] = None
    category_name: Optional[str] = None
    has_similar: Optional[bool] = False
    has_image: Optional[bool] = False


class CategoryNews(BaseModel):
    category: str
    category_code: str
    news: List[EntryWithSource]


class NewsResponse(BaseModel):
    categories: List[CategoryNews]


class SimilarNewsResponse(BaseModel):
    original: EntryWithSource
    similar_sources: List[EntryWithSource]


class PaginatedNewsResponse(BaseModel):
    items: List[EntryWithSource]
    total: int
    has_more: bool


class HomeScreenResponse(BaseModel):
    categories: List[CategoryNews]
