import 'package:flutter/foundation.dart';

enum Environment {
  development,
  staging,
  production,
}

class EnvironmentService {
  static Environment get current {
    // Check for environment variable first (most explicit)
    const envString = String.fromEnvironment('ENVIRONMENT');
    if (envString.isNotEmpty) {
      switch (envString.toLowerCase()) {
        case 'development':
        case 'dev':
          return Environment.development;
        case 'staging':
        case 'stage':
          return Environment.staging;
        case 'production':
        case 'prod':
          return Environment.production;
      }
    }

    // For local development, always use development environment
    // unless explicitly overridden with environment variable
    if (kDebugMode || kProfileMode) {
      return Environment.development;
    }

    // Release mode - still default to development for local builds
    // Only use production when explicitly set via environment variable
    return Environment.development;
  }

  static Map<Environment, Map<String, dynamic>> get _config => {
        Environment.development: {
          'apiBaseUrl': _getLocalApiUrl(),
          'debug': true,
          'description': 'Local development server',
          'timeout': 30000, // 30 seconds for development
        },
        Environment.staging: {
          'apiBaseUrl': 'https://api.breakingbright.de',
          'debug': true,
          'description': 'Production server with debug logging',
          'timeout': 15000, // 15 seconds for staging
        },
        Environment.production: {
          'apiBaseUrl': 'https://api.breakingbright.de',
          'debug': false,
          'description': 'Production server',
          'timeout': 10000, // 10 seconds for production
        },
      };

  static String _getLocalApiUrl() {
    // Try to detect the best local URL based on platform
    if (kIsWeb) {
      // Web development - use localhost
      return 'http://localhost:8000';
    } else {
      // Mobile development - use your local IP
      return 'http://*************:8000';
    }
  }

  static Map<String, dynamic> get config => _config[current]!;

  static String get apiBaseUrl => config['apiBaseUrl'] as String;
  static bool get isDebug => config['debug'] as bool;
  static String get description => config['description'] as String;
  static int get timeoutMs => config['timeout'] as int;

  static void logEnvironmentInfo() {
    if (isDebug) {
      debugPrint('🌍 Environment: ${current.name}');
      debugPrint('� lAPI Base URL: $apiBaseUrl');
      debugPrint('📝 Description: $description');
      debugPrint('⏱️ Timeout: ${timeoutMs}ms');
      debugPrint('🐛 Debug Mode: $isDebug');

      if (kIsWeb) {
        debugPrint('🌐 Platform: Web');
      } else {
        debugPrint('📱 Platform: Mobile/Desktop');
      }
    }
  }
}
