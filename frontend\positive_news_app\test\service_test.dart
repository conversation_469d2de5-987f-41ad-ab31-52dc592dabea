import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/models/news_model.dart';
import 'package:breakingbright/services/favorites_service.dart';
import 'package:breakingbright/services/similar_news_service.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:breakingbright/services/api_service.dart';

@GenerateMocks([ApiService])
import 'service_test.mocks.dart';

void main() {
  group('FavoritesService Tests', () {
    late FavoritesService favoritesService;
    
    setUp(() {
      favoritesService = FavoritesService();
    });
    
    test('Favorit hinzufügen und prüfen', () {
      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        published: DateTime.now(),
        llmPositive: 0.95,
        llmReasonList: 'Test reason',
        hasSimilar: null, // Updated for lazy loading
      );
      
      favoritesService.addFavorite(news);
      
      expect(favoritesService.isFavorite(news), true);
      expect(favoritesService.favorites.length, 1);
      expect(favoritesService.favorites[0].entryId, 'test_id');
    });
    
    test('Favorit entfernen', () {
      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        published: DateTime.now(),
        llmPositive: 0.95,
        hasSimilar: null, // Updated for lazy loading
      );
      
      favoritesService.addFavorite(news);
      favoritesService.removeFavorite(news);
      
      expect(favoritesService.isFavorite(news), false);
      expect(favoritesService.favorites.length, 0);
    });
    
    test('Alle Favoriten löschen', () {
      final news1 = EntryWithSource(
        entryId: 'test_id_1',
        title: 'Test Nachricht 1',
        link: 'https://example.com/1',
        source: 'test_source',
        published: DateTime.now(),
        llmPositive: 0.95,
        llmReasonList: 'Test reason 1',
        hasSimilar: null, // Updated for lazy loading
      );

      final news2 = EntryWithSource(
        entryId: 'test_id_2',
        title: 'Test Nachricht 2',
        link: 'https://example.com/2',
        source: 'test_source',
        published: DateTime.now(),
        llmPositive: 0.92,
        llmReasonList: 'Test reason 2',
        hasSimilar: null, // Updated for lazy loading
      );
      
      favoritesService.addFavorite(news1);
      favoritesService.addFavorite(news2);
      
      favoritesService.clearFavorites();
      
      expect(favoritesService.favorites.length, 0);
    });
  });
  
  group('ApiService Tests mit Mocks', () {
    late MockApiService mockApiService;
    
    setUp(() {
      mockApiService = MockApiService();
    });
    
    test('getNewsByCategories liefert erwartete Daten', () async {
      final categoryNews = CategoryNews(
        category: 'Politik',
        categoryCode: 'POL',
        news: [
          EntryWithSource(
            entryId: 'test_id_1',
            title: 'Test Nachricht 1',
            link: 'https://example.com/1',
            source: 'test_source',
            published: DateTime.now(),
            llmPositive: 0.95,
            llmReasonList: 'Test reason',
            hasSimilar: null, // Updated for lazy loading
          ),
        ],
      );
      
      final newsResponse = NewsResponse(
        categories: [categoryNews],
      );
      
      when(mockApiService.getNewsByCategories()).thenAnswer((_) async => newsResponse);
      
      final result = await mockApiService.getNewsByCategories();
      
      expect(result.categories.length, 1);
      expect(result.categories[0].category, 'POL');
      expect(result.categories[0].news.length, 1);
      expect(result.categories[0].news[0].title, 'Test Nachricht 1');
      
      verify(mockApiService.getNewsByCategories()).called(1);
    });
    
    test('getSimilarNews liefert erwartete Daten', () async {
      final originalNews = EntryWithSource(
        entryId: 'test_id_1',
        title: 'Original Nachricht',
        link: 'https://example.com/1',
        source: 'test_source',
        published: DateTime.now(),
        llmPositive: 0.95,
        llmReasonList: 'Original reason',
        hasSimilar: null, // Updated for lazy loading
      );

      final similarNews = EntryWithSource(
        entryId: 'test_id_2',
        title: 'Ähnliche Nachricht',
        link: 'https://example.com/2',
        source: 'other_source',
        published: DateTime.now(),
        llmPositive: 0.92,
        llmReasonList: 'Similar reason',
        hasSimilar: null, // Updated for lazy loading
      );
      
      final similarNewsResponse = SimilarNewsResponse(
        original: originalNews,
        similarSources: [similarNews],
      );
      
      when(mockApiService.getSimilarNews('test_id_1')).thenAnswer((_) async => similarNewsResponse);
      
      final result = await mockApiService.getSimilarNews('test_id_1');
      
      expect(result.original.title, 'Original Nachricht');
      expect(result.similarSources.length, 1);
      expect(result.similarSources[0].title, 'Ähnliche Nachricht');
      
      verify(mockApiService.getSimilarNews('test_id_1')).called(1);
    });

    test('checkSimilarNews liefert erwartete Batch-Daten', () async {
      final entryIds = ['test_id_1', 'test_id_2', 'test_id_3'];
      final expectedResult = {
        'test_id_1': true,
        'test_id_2': false,
        'test_id_3': true,
      };
      
      when(mockApiService.checkSimilarNews(entryIds))
          .thenAnswer((_) async => expectedResult);
      
      final result = await mockApiService.checkSimilarNews(entryIds);
      
      expect(result.length, 3);
      expect(result['test_id_1'], true);
      expect(result['test_id_2'], false);
      expect(result['test_id_3'], true);
      
      verify(mockApiService.checkSimilarNews(entryIds)).called(1);
    });

    test('hasSimilarNews verwendet checkSimilarNews intern', () async {
      when(mockApiService.checkSimilarNews(['test_id_1']))
          .thenAnswer((_) async => {'test_id_1': true});
      
      final result = await mockApiService.hasSimilarNews('test_id_1');
      
      expect(result, true);
      verify(mockApiService.checkSimilarNews(['test_id_1'])).called(1);
    });
  });

  group('SimilarNewsService Tests', () {
    late SimilarNewsService similarNewsService;
    late MockApiService mockApiService;
    
    setUp(() {
      mockApiService = MockApiService();
      similarNewsService = SimilarNewsService(mockApiService);
    });
    
    test('loadSimilarFlags lädt und cached Flags korrekt', () async {
      final entryIds = ['test_id_1', 'test_id_2'];
      final expectedFlags = {
        'test_id_1': true,
        'test_id_2': false,
      };
      
      when(mockApiService.checkSimilarNews(entryIds))
          .thenAnswer((_) async => expectedFlags);
      
      await similarNewsService.loadSimilarFlags(entryIds);
      
      expect(similarNewsService.getSimilarFlag('test_id_1'), true);
      expect(similarNewsService.getSimilarFlag('test_id_2'), false);
      expect(similarNewsService.isLoaded('test_id_1'), true);
      expect(similarNewsService.isLoaded('test_id_2'), true);
      
      verify(mockApiService.checkSimilarNews(entryIds)).called(1);
    });
    
    test('loadSimilarFlags vermeidet doppelte Anfragen', () async {
      final entryIds = ['test_id_1', 'test_id_2'];
      final expectedFlags = {
        'test_id_1': true,
        'test_id_2': false,
      };
      
      when(mockApiService.checkSimilarNews(entryIds))
          .thenAnswer((_) async => expectedFlags);
      
      // Erste Anfrage
      await similarNewsService.loadSimilarFlags(entryIds);
      
      // Zweite Anfrage sollte keine API-Anfrage auslösen
      await similarNewsService.loadSimilarFlags(entryIds);
      
      verify(mockApiService.checkSimilarNews(entryIds)).called(1);
    });
    
    test('loadSimilarFlag lädt einzelne Flag', () async {
      when(mockApiService.checkSimilarNews(['test_id_1']))
          .thenAnswer((_) async => {'test_id_1': true});
      
      final result = await similarNewsService.loadSimilarFlag('test_id_1');
      
      expect(result, true);
      expect(similarNewsService.getSimilarFlag('test_id_1'), true);
      expect(similarNewsService.isLoaded('test_id_1'), true);
      
      verify(mockApiService.checkSimilarNews(['test_id_1'])).called(1);
    });
    
    test('updateNewsWithSimilarFlags aktualisiert News-Items korrekt', () async {
      // Setup cached flags
      final entryIds = ['test_id_1', 'test_id_2'];
      final expectedFlags = {
        'test_id_1': true,
        'test_id_2': false,
      };
      
      when(mockApiService.checkSimilarNews(entryIds))
          .thenAnswer((_) async => expectedFlags);
      
      await similarNewsService.loadSimilarFlags(entryIds);
      
      // Create news items with null hasSimilar
      final newsItems = [
        EntryWithSource(
          entryId: 'test_id_1',
          title: 'Test 1',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.now(),
          hasSimilar: null,
        ),
        EntryWithSource(
          entryId: 'test_id_2',
          title: 'Test 2',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.now(),
          hasSimilar: null,
        ),
      ];
      
      final updatedNews = similarNewsService.updateNewsWithSimilarFlags(newsItems);
      
      expect(updatedNews[0].hasSimilar, true);
      expect(updatedNews[1].hasSimilar, false);
    });
    
    test('preloadSimilarFlags lädt nur null-Flags', () async {
      final newsItems = [
        EntryWithSource(
          entryId: 'test_id_1',
          title: 'Test 1',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.now(),
          hasSimilar: null, // Should be loaded
        ),
        EntryWithSource(
          entryId: 'test_id_2',
          title: 'Test 2',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.now(),
          hasSimilar: true, // Should be skipped
        ),
      ];
      
      when(mockApiService.checkSimilarNews(['test_id_1']))
          .thenAnswer((_) async => {'test_id_1': false});
      
      await similarNewsService.preloadSimilarFlags(newsItems);
      
      // Only test_id_1 should have been loaded
      verify(mockApiService.checkSimilarNews(['test_id_1'])).called(1);
      expect(similarNewsService.isLoaded('test_id_1'), true);
      expect(similarNewsService.isLoaded('test_id_2'), false);
    });
    
    test('clear löscht alle cached Daten', () async {
      final entryIds = ['test_id_1'];
      when(mockApiService.checkSimilarNews(entryIds))
          .thenAnswer((_) async => {'test_id_1': true});
      
      await similarNewsService.loadSimilarFlags(entryIds);
      expect(similarNewsService.isLoaded('test_id_1'), true);
      
      similarNewsService.clear();
      
      expect(similarNewsService.getSimilarFlag('test_id_1'), null);
      expect(similarNewsService.isLoaded('test_id_1'), false);
      expect(similarNewsService.isLoading('test_id_1'), false);
    });
    
    test('Fehlerbehandlung bei API-Fehlern', () async {
      final entryIds = ['test_id_1'];
      
      when(mockApiService.checkSimilarNews(entryIds))
          .thenThrow(Exception('Network error'));
      
      await similarNewsService.loadSimilarFlags(entryIds);
      
      // Should be marked as loaded even on error to prevent infinite retries
      expect(similarNewsService.isLoaded('test_id_1'), true);
      expect(similarNewsService.getSimilarFlag('test_id_1'), null);
    });
  });

  group('Lazy Loading Integration Tests', () {
    test('EntryWithSource.fromJson behandelt null hasSimilar korrekt', () {
      final jsonWithNull = {
        'entry_id': 'test_id',
        'title': 'Test Title',
        'link': 'https://example.com',
        'source': 'test_source',
        'published': '2024-01-01T12:00:00Z',
        'has_similar': null,
      };
      
      final entry = EntryWithSource.fromJson(jsonWithNull);
      
      expect(entry.hasSimilar, null);
    });
    
    test('EntryWithSource.fromJson behandelt true/false hasSimilar korrekt', () {
      final jsonWithTrue = {
        'entry_id': 'test_id_1',
        'title': 'Test Title 1',
        'link': 'https://example.com/1',
        'source': 'test_source',
        'published': '2024-01-01T12:00:00Z',
        'has_similar': true,
      };
      
      final jsonWithFalse = {
        'entry_id': 'test_id_2',
        'title': 'Test Title 2',
        'link': 'https://example.com/2',
        'source': 'test_source',
        'published': '2024-01-01T12:00:00Z',
        'has_similar': false,
      };
      
      final entryTrue = EntryWithSource.fromJson(jsonWithTrue);
      final entryFalse = EntryWithSource.fromJson(jsonWithFalse);
      
      expect(entryTrue.hasSimilar, true);
      expect(entryFalse.hasSimilar, false);
    });
  });
}
