"""
Advanced test runner with multiple execution modes
Provides flexible test execution with filtering, reporting, and configuration options
"""

import unittest
import sys
import os
import argparse
import time
from datetime import datetime
import json

# Add paths for imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
etl_path = os.path.dirname(__file__)
sys.path.append(etl_path)

from test_config import TestConfig, setup_test_environment


class TestRunner:
    """Advanced test runner with multiple execution modes"""
    
    def __init__(self):
        self.config = TestConfig()
        self.start_time = None
        self.end_time = None
        self.results = None
        
    def discover_tests(self, pattern="test_*.py", start_dir=None):
        """Discover tests matching the pattern"""
        if start_dir is None:
            start_dir = os.path.dirname(__file__)
        
        loader = unittest.TestLoader()
        suite = loader.discover(start_dir, pattern=pattern)
        return suite
    
    def load_specific_tests(self, test_modules):
        """Load specific test modules"""
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        for module_name in test_modules:
            try:
                module = __import__(module_name)
                suite.addTests(loader.loadTestsFromModule(module))
                print(f"✓ Loaded {module_name}")
            except ImportError as e:
                print(f"⚠ Could not load {module_name}: {e}")
        
        return suite
    
    def run_tests(self, suite, verbosity=2, failfast=False):
        """Run the test suite with specified options"""
        self.start_time = time.time()
        
        # Create custom test runner
        stream = sys.stdout
        runner = unittest.TextTestRunner(
            stream=stream,
            verbosity=verbosity,
            failfast=failfast,
            buffer=True  # Capture stdout/stderr during tests
        )
        
        print(f"Starting test run at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Platform: {self.config.IS_WINDOWS and 'Windows' or 'Unix'}")
        print(f"Python version: {sys.version}")
        print("=" * 80)
        
        self.results = runner.run(suite)
        self.end_time = time.time()
        
        return self.results
    
    def generate_report(self, output_file=None):
        """Generate a detailed test report"""
        if not self.results:
            print("No test results available")
            return
        
        duration = self.end_time - self.start_time
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'duration_seconds': round(duration, 2),
            'platform': 'Windows' if self.config.IS_WINDOWS else 'Unix',
            'python_version': sys.version,
            'summary': {
                'total_tests': self.results.testsRun,
                'passed': self.results.testsRun - len(self.results.failures) - len(self.results.errors),
                'failed': len(self.results.failures),
                'errors': len(self.results.errors),
                'skipped': len(getattr(self.results, 'skipped', [])),
                'success_rate': round((self.results.testsRun - len(self.results.failures) - len(self.results.errors)) / max(self.results.testsRun, 1) * 100, 1)
            },
            'failures': [
                {
                    'test': str(test),
                    'error': traceback.split('\n')[-2] if traceback else 'Unknown error'
                }
                for test, traceback in self.results.failures
            ],
            'errors': [
                {
                    'test': str(test),
                    'error': traceback.split('\n')[-2] if traceback else 'Unknown error'
                }
                for test, traceback in self.results.errors
            ]
        }
        
        # Print summary
        self.print_summary(report)
        
        # Save to file if requested
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"\nDetailed report saved to: {output_file}")
        
        return report
    
    def print_summary(self, report):
        """Print a formatted summary of test results"""
        print("\n" + "=" * 80)
        print("TEST EXECUTION SUMMARY")
        print("=" * 80)
        
        summary = report['summary']
        print(f"Execution time: {report['duration_seconds']} seconds")
        print(f"Platform: {report['platform']}")
        print(f"Total tests: {summary['total_tests']}")
        print(f"✓ Passed: {summary['passed']}")
        print(f"✗ Failed: {summary['failed']}")
        print(f"⚠ Errors: {summary['errors']}")
        print(f"⊘ Skipped: {summary['skipped']}")
        print(f"Success rate: {summary['success_rate']}%")
        
        # Status indicator
        if summary['success_rate'] >= 90:
            print("🎉 Excellent! Test suite is in great shape!")
        elif summary['success_rate'] >= 80:
            print("✅ Good! Test suite is in good shape!")
        elif summary['success_rate'] >= 70:
            print("⚠ Warning! Test suite needs attention!")
        else:
            print("❌ Critical! Test suite needs significant work!")
        
        # Show failures and errors
        if report['failures']:
            print(f"\nFAILURES ({len(report['failures'])}):")
            for failure in report['failures'][:5]:  # Show first 5
                print(f"  - {failure['test']}: {failure['error']}")
            if len(report['failures']) > 5:
                print(f"  ... and {len(report['failures']) - 5} more")
        
        if report['errors']:
            print(f"\nERRORS ({len(report['errors'])}):")
            for error in report['errors'][:5]:  # Show first 5
                print(f"  - {error['test']}: {error['error']}")
            if len(report['errors']) > 5:
                print(f"  ... and {len(report['errors']) - 5} more")


def main():
    """Main entry point for the test runner"""
    parser = argparse.ArgumentParser(description='Advanced test runner for NewsAggregator')
    
    # Test selection options
    parser.add_argument('--pattern', default='test_*.py', 
                       help='Pattern for test discovery (default: test_*.py)')
    parser.add_argument('--modules', nargs='+', 
                       help='Specific test modules to run')
    parser.add_argument('--category', choices=['basic', 'integration', 'advanced', 'external', 'database', 'fixes'],
                       help='Run tests from a specific category')
    
    # Execution options
    parser.add_argument('--verbosity', type=int, default=2, choices=[0, 1, 2],
                       help='Test output verbosity (0=quiet, 1=normal, 2=verbose)')
    parser.add_argument('--failfast', action='store_true',
                       help='Stop on first failure')
    parser.add_argument('--no-skip', action='store_true',
                       help='Run all tests, ignoring skip conditions')
    
    # Output options
    parser.add_argument('--report', metavar='FILE',
                       help='Save detailed report to JSON file')
    parser.add_argument('--quiet', action='store_true',
                       help='Minimal output, only show summary')
    
    # Environment options
    parser.add_argument('--skip-slow', action='store_true',
                       help='Skip slow tests')
    parser.add_argument('--skip-external', action='store_true',
                       help='Skip external service tests')
    parser.add_argument('--skip-multiprocessing', action='store_true',
                       help='Skip multiprocessing tests')
    
    args = parser.parse_args()
    
    # Set environment variables based on arguments
    if args.skip_slow:
        os.environ['SKIP_SLOW_TESTS'] = 'true'
    if args.skip_external:
        os.environ['SKIP_EXTERNAL_TESTS'] = 'true'
    if args.skip_multiprocessing:
        os.environ['SKIP_MULTIPROCESSING_TESTS'] = 'true'
    
    # Initialize test runner
    runner = TestRunner()
    
    # Determine which tests to run
    if args.modules:
        suite = runner.load_specific_tests(args.modules)
    elif args.category:
        category_modules = {
            'basic': ['test_simple'],
            'integration': ['test_integration'],
            'advanced': ['test_advanced'],
            'external': ['test_external_services'],
            'database': ['test_database_operations'],
            'fixes': ['test_fixes']
        }
        suite = runner.load_specific_tests(category_modules[args.category])
    else:
        suite = runner.discover_tests(pattern=args.pattern)
    
    # Run tests
    verbosity = 0 if args.quiet else args.verbosity
    results = runner.run_tests(suite, verbosity=verbosity, failfast=args.failfast)
    
    # Generate report
    runner.generate_report(output_file=args.report)
    
    # Exit with appropriate code
    sys.exit(0 if results.wasSuccessful() else 1)


if __name__ == '__main__':
    setup_test_environment()
    main()