"""
Comprehensive unit tests for NewsAggregator ETL functionality

This file contains the complete test suite for the NewsAggregator class.
For easier testing, use the individual test files:
- test_simple.py: Basic functionality tests
- test_integration.py: Integration tests with actual class
- run_all_tests.py: Complete test runner with summary

To run all tests: python etl/run_all_tests.py
"""

import unittest
import sys
import os

# Add paths for imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
etl_path = os.path.dirname(__file__)
sys.path.append(etl_path)

# Import all test classes
try:
    from test_simple import TestNewsAggregatorBasics, TestMockingPatterns
    from test_integration import TestNewsAggregatorIntegration, TestHelperFunctions
    from test_advanced import (TestConfigurationValidation, TestErrorHandling, 
                             TestEdgeCases, TestDataProcessing, 
                             TestConcurrencyAndThreading, TestPerformanceConsiderations)
    from test_external_services import (TestRSSFeedProcessing, TestTranslationService,
                                      TestMLModelIntegration, TestLLMIntegration,
                                      TestWebScrapingIntegration)
    from test_database_operations import (TestDatabaseConnections, TestDatabaseQueries,
                                        TestDatabaseTransactions, TestDataFrameOperations,
                                        TestMultiprocessingOperations, TestDatabasePerformance)
    
    # Create a combined test suite
    def create_test_suite():
        """Create a comprehensive test suite"""
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        # Add basic test classes
        suite.addTests(loader.loadTestsFromTestCase(TestNewsAggregatorBasics))
        suite.addTests(loader.loadTestsFromTestCase(TestMockingPatterns))
        suite.addTests(loader.loadTestsFromTestCase(TestNewsAggregatorIntegration))
        suite.addTests(loader.loadTestsFromTestCase(TestHelperFunctions))
        
        # Add advanced test classes
        suite.addTests(loader.loadTestsFromTestCase(TestConfigurationValidation))
        suite.addTests(loader.loadTestsFromTestCase(TestErrorHandling))
        suite.addTests(loader.loadTestsFromTestCase(TestEdgeCases))
        suite.addTests(loader.loadTestsFromTestCase(TestDataProcessing))
        suite.addTests(loader.loadTestsFromTestCase(TestConcurrencyAndThreading))
        suite.addTests(loader.loadTestsFromTestCase(TestPerformanceConsiderations))
        
        # Add external service test classes
        suite.addTests(loader.loadTestsFromTestCase(TestRSSFeedProcessing))
        suite.addTests(loader.loadTestsFromTestCase(TestTranslationService))
        suite.addTests(loader.loadTestsFromTestCase(TestMLModelIntegration))
        suite.addTests(loader.loadTestsFromTestCase(TestLLMIntegration))
        suite.addTests(loader.loadTestsFromTestCase(TestWebScrapingIntegration))
        
        # Add database operation test classes
        suite.addTests(loader.loadTestsFromTestCase(TestDatabaseConnections))
        suite.addTests(loader.loadTestsFromTestCase(TestDatabaseQueries))
        suite.addTests(loader.loadTestsFromTestCase(TestDatabaseTransactions))
        suite.addTests(loader.loadTestsFromTestCase(TestDataFrameOperations))
        suite.addTests(loader.loadTestsFromTestCase(TestMultiprocessingOperations))
        suite.addTests(loader.loadTestsFromTestCase(TestDatabasePerformance))
        
        return suite
    
    if __name__ == '__main__':
        # Run the complete test suite
        runner = unittest.TextTestRunner(verbosity=2)
        suite = create_test_suite()
        result = runner.run(suite)
        
        # Print summary
        print(f"\nTests run: {result.testsRun}")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
        
        if result.wasSuccessful():
            print("✓ All tests passed!")
        else:
            print("✗ Some tests failed")
            
except ImportError as e:
    print(f"Could not import test modules: {e}")
    print("Please run individual test files or use run_all_tests.py")
    
    if __name__ == '__main__':
        # Fallback: just run a simple test
        class BasicTest(unittest.TestCase):
            def test_import_check(self):
                """Basic test to verify test framework works"""
                self.assertTrue(True)
                
        unittest.main()