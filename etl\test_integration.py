"""
Integration tests for NewsAggregator
Tests the actual class with proper mocking
"""

import unittest
from unittest.mock import Mock, patch, mock_open
import os
import sys
from datetime import datetime, timezone

# Add paths for imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
etl_path = os.path.dirname(__file__)
sys.path.append(etl_path)


class TestNewsAggregatorIntegration(unittest.TestCase):
    """Integration tests for NewsAggregator class"""

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    def test_newsaggregator_init(self, mock_scoped_session, mock_create_engine, mock_settings):
        """Test NewsAggregator initialization"""
        # Mock settings
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        
        # Mock database components
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_factory = Mock()
        mock_scoped_session.return_value = mock_session_factory
        
        try:
            from news_aggregator import NewsAggregator
            
            # Test initialization without clearing entries
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            # Verify engine and session creation
            mock_create_engine.assert_called_once()
            mock_scoped_session.assert_called_once()
            
            # Verify attributes are set
            self.assertEqual(aggregator.engine, mock_engine)
            self.assertEqual(aggregator.Session, mock_session_factory)
            
        except ImportError:
            self.skipTest("NewsAggregator import failed - skipping integration test")

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    def test_generate_entry_id_method(self, mock_scoped_session, mock_create_engine, mock_settings):
        """Test the _generate_entry_id method"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            # Create mock entry
            mock_entry = Mock()
            mock_entry.title = "Test Article"
            mock_entry.link = "https://example.com/test"
            
            # Create source config
            source_config = {'entry_id_from': 'title,link'}
            
            # Test ID generation
            entry_id = aggregator._generate_entry_id(mock_entry, source_config)
            
            # Verify result
            self.assertIsInstance(entry_id, str)
            self.assertEqual(len(entry_id), 64)  # SHA256 hash length
            
        except ImportError:
            self.skipTest("NewsAggregator import failed - skipping integration test")

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    @patch('builtins.open', new_callable=mock_open, read_data="""
rss_sources:
  - url: "https://example.com/rss"
    entry_id_from: "title,link"
  - url: "https://test.com/feed"
    entry_id_from: "link"
""")
    @patch('os.path.isabs')
    @patch('os.path.join')
    def test_load_rss_sources_method(self, mock_join, mock_isabs, mock_file, 
                                   mock_scoped_session, mock_create_engine, mock_settings):
        """Test the _load_rss_sources method"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        mock_settings.NEWS_RSS_SOURCES_FILE = "test_sources.yaml"
        mock_isabs.return_value = False
        mock_join.return_value = "etl/test_sources.yaml"
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            # Test RSS sources loading
            sources = aggregator._load_rss_sources()
            
            # Verify results
            self.assertEqual(len(sources), 2)
            self.assertEqual(sources[0]['url'], "https://example.com/rss")
            self.assertEqual(sources[1]['url'], "https://test.com/feed")
            
        except ImportError:
            self.skipTest("NewsAggregator import failed - skipping integration test")

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    def test_clean_html_content_method(self, mock_scoped_session, mock_create_engine, mock_settings):
        """Test the _clean_html_content method"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            # Test HTML cleaning
            html_content = "<p>This is a test</p><br><div>Another paragraph</div>"
            cleaned = aggregator._clean_html_content(html_content)
            
            # Verify cleaning worked
            self.assertNotIn('<', cleaned)
            self.assertNotIn('>', cleaned)
            self.assertIn('This is a test', cleaned)
            self.assertIn('Another paragraph', cleaned)
            
            # Test with empty content
            self.assertEqual(aggregator._clean_html_content(""), "")
            self.assertEqual(aggregator._clean_html_content(None), "")
            
        except ImportError:
            self.skipTest("NewsAggregator import failed - skipping integration test")

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    def test_session_scope_context_manager(self, mock_scoped_session, mock_create_engine, mock_settings):
        """Test the session_scope context manager"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        
        # Mock session
        mock_session = Mock()
        mock_session_factory = Mock()
        mock_session_factory.return_value = mock_session
        mock_scoped_session.return_value = mock_session_factory
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            # Test successful context manager usage
            with aggregator.session_scope() as session:
                self.assertEqual(session, mock_session)
                # Simulate some database operation
                session.query("test")
            
            # Verify session methods were called
            mock_session.commit.assert_called()
            mock_session.close.assert_called()
            
        except ImportError:
            self.skipTest("NewsAggregator import failed - skipping integration test")

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    def test_session_scope_with_exception(self, mock_scoped_session, mock_create_engine, mock_settings):
        """Test session_scope context manager with exception handling"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        
        # Mock session
        mock_session = Mock()
        mock_session_factory = Mock()
        mock_session_factory.return_value = mock_session
        mock_session_factory.remove = Mock()
        mock_scoped_session.return_value = mock_session_factory
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            # Test exception handling
            with self.assertRaises(ValueError):
                with aggregator.session_scope() as session:
                    raise ValueError("Test exception")
            
            # Verify rollback and cleanup were called
            mock_session.rollback.assert_called()
            mock_session.close.assert_called()
            mock_session_factory.remove.assert_called()
            
        except ImportError:
            self.skipTest("NewsAggregator import failed - skipping integration test")


class TestHelperFunctions(unittest.TestCase):
    """Test helper functions from news_aggregator module"""

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.sessionmaker')
    def test_get_entries_in_process_function(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test _get_entries_in_process helper function"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        mock_settings.NEWS_LOOKBACK_DAYS = 1
        
        try:
            from news_aggregator import _get_entries_in_process
            
            # Mock database components
            mock_engine = Mock()
            mock_create_engine.return_value = mock_engine
            mock_session = Mock()
            mock_sessionmaker.return_value = mock_session
            
            # Mock query results
            mock_entry = Mock()
            mock_entry.__dict__ = {
                'entry_id': 'test123', 
                'title': 'Test Article',
                '_sa_instance_state': 'should_be_removed'
            }
            
            mock_query = Mock()
            mock_query.filter.return_value = mock_query
            mock_query.order_by.return_value = mock_query
            mock_query.all.return_value = [mock_entry]
            mock_session.query.return_value = mock_query
            
            # Mock parquet writing
            with patch('pyarrow.parquet.write_table') as mock_write_table:
                result = _get_entries_in_process(
                    mandatory_fields=['title'],
                    analysis_fields=['full_text'],
                    filters=[],
                    parquet_path='/tmp/test.parquet'
                )
                
                self.assertTrue(result)
                mock_write_table.assert_called_once()
                
        except ImportError:
            self.skipTest("Helper function import failed - skipping test")


if __name__ == '__main__':
    # Run tests with verbose output
    unittest.main(verbosity=2)