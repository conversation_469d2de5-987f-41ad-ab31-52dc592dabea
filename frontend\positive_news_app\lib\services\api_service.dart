import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:breakingbright/models/news_model.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:breakingbright/providers/settings_provider.dart';
import 'package:flutter/foundation.dart' show defaultTargetPlatform, TargetPlatform;
import 'dart:io';

class ApiService {
  // Debug flag - set to true to see connection details
  final bool _debug = true;
  
  // Basis-URL für die API - wird für die Produktion angepasst
  // ignore: unused_field
  final String _localBaseUrl = "http://192.168.3.123:8000";
  
  // Für Produktion
  // ignore: unused_field
  final String _productionBaseUrl = 'http://api.breakingbright.de';
  
  // HTTP-Client für Anfragen
  final http.Client _client = http.Client();

  // Gibt die richtige Basis-URL basierend auf der Umgebung zurück
  String get baseUrl {
    // For debugging, log the URL being used
    String baseUrl = _localBaseUrl;
    _logDebug('Using base URL: $baseUrl');
    return baseUrl;
  }
  
  /// Logs a debug message if [_debug] is true.
  ///
  /// This can be set to true in development to see the URLs being used for
  /// API requests.
  void _logDebug(String message) {
    if (_debug) {
      debugPrint('ApiService: $message');
    }
  }

  // Holt Nachrichten nach Kategorien
  Future<NewsResponse> getNewsByCategories({
    double minPositive = 0.7,
    int limitPerCategory = 3,
    BuildContext? context,
  }) async {
    final lookbackHours = Provider.of<SettingsProvider>(context!, listen: false).lookbackHours;
    try {
      final response = await _client.get(
        Uri.parse('$baseUrl/news/categories/?min_positive=$minPositive&hours=$lookbackHours&limit_per_category=$limitPerCategory'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return NewsResponse.fromJson(json.decode(utf8.decode(response.bodyBytes)));
      } else {
        throw Exception('Fehler beim Laden der Nachrichten: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Netzwerkfehler: $e');
    }
  }

  // Holt Nachrichten mit Filteroptionen und Pagination
  Future<PaginatedNewsResponse> getNews({
    int skip = 0,
    int limit = 10,
    double minPositive = 0.7,
    String? category,
    BuildContext? context,
  }) async {
    final lookbackHours = Provider.of<SettingsProvider>(context!, listen: false).lookbackHours;
    try {
      String url = '$baseUrl/news/?skip=$skip&limit=$limit&min_positive=$minPositive&hours=$lookbackHours';
      if (category != null) {
        url += '&category=$category';
      }

      final response = await _client.get(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return PaginatedNewsResponse.fromJson(json.decode(utf8.decode(response.bodyBytes)));
      } else {
        throw Exception('Fehler beim Laden der Nachrichten: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Netzwerkfehler: $e');
    }
  }

  // Holt Nachrichten einer bestimmten Kategorie mit Pagination
  Future<PaginatedNewsResponse> getNewsByCategory({
    required String categoryCode,
    int skip = 0,
    int limit = 10,
    double minPositive = 0.7,
    required int lookbackHours,
  }) async {
    try {
      final response = await _client.get(
        Uri.parse('$baseUrl/news/category/$categoryCode?skip=$skip&limit=$limit&min_positive=$minPositive&hours=$lookbackHours'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return PaginatedNewsResponse.fromJson(json.decode(utf8.decode(response.bodyBytes)));
      } else {
        throw Exception('Fehler beim Laden der Kategorienachrichten: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Netzwerkfehler: $e');
    }
  }

  // Holt ähnliche Nachrichten zu einer bestimmten Nachricht
  Future<SimilarNewsResponse> getSimilarNews(String entryId) async {
    try {
      final response = await _client.get(
        Uri.parse('$baseUrl/news/$entryId/similar'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return SimilarNewsResponse.fromJson(json.decode(utf8.decode(response.bodyBytes)));
      } else {
        throw Exception('Fehler beim Laden ähnlicher Nachrichten: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Netzwerkfehler: $e');
    }
  }

  // Prüft, ob es ähnliche Nachrichten zu einer bestimmten Nachricht gibt
  Future<bool> hasSimilarNews(String entryId) async {
    try {
      final response = await _client.get(
        Uri.parse('$baseUrl/news/$entryId/has_similar'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return json.decode(utf8.decode(response.bodyBytes)) as bool;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  // Sucht nach Nachrichten mit einem Suchbegriff
  Future<PaginatedNewsResponse> searchNews(String query) async {
    try {
      final response = await _client.get(
        Uri.parse('$baseUrl/news/?search=$query'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return PaginatedNewsResponse.fromJson(json.decode(utf8.decode(response.bodyBytes)));
      } else {
        throw Exception('Fehler bei der Suche: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Netzwerkfehler: $e');
    }
  }
  
  // Testet die Verbindung zum Backend
  Future<bool> testConnection() async {
    try {
      final response = await _client.get(Uri.parse('$baseUrl/'));
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  Future<NewsResponse> getHomeScreenData({
    double minPositive = 0.7,
    int limitPerCategory = 3,
    BuildContext? context,
  }) async {
    final lookbackHours = Provider.of<SettingsProvider>(context!, listen: false).lookbackHours;
    try {
      // Android has issues with the standard HTTP client, use a different approach
      if (defaultTargetPlatform == TargetPlatform.android) {
        _logDebug('Using Android-specific HTTP implementation');
        // Use a direct HttpClient from dart:io for Android
        final httpClient = HttpClient();
        final request = await httpClient.getUrl(
          Uri.parse('$baseUrl/news/home?min_positive=$minPositive&hours=$lookbackHours&limit_per_category=$limitPerCategory')
        );
        request.headers.set('Content-Type', 'application/json');
        final httpResponse = await request.close();
        final responseBody = await httpResponse.transform(utf8.decoder).join();
        httpClient.close();
        
        if (httpResponse.statusCode == 200) {
          return NewsResponse.fromJson(json.decode(responseBody));
        } else {
          throw Exception('Fehler beim Laden der Startseite: ${httpResponse.statusCode}');
        }
      } else {
        final response = await _client.get(
          Uri.parse('$baseUrl/news/home?min_positive=$minPositive&hours=$lookbackHours&limit_per_category=$limitPerCategory'),
          headers: {'Content-Type': 'application/json'},
        );
        if (response.statusCode == 200) {
          return NewsResponse.fromJson(json.decode(utf8.decode(response.bodyBytes)));
        } else {
          throw Exception('Fehler beim Laden der Startseite: ${response.statusCode} - ${response.body}');
        }
      }
    } catch (e) {
      throw Exception('Netzwerkfehler: $e');
    }
  }

  // Enhanced method to handle both API images and fallbacks
  String getImageUrl(String entryId, {bool hasImage = false}) {
    return hasImage 
        ? '$baseUrl/news/$entryId/image'
        : 'https://picsum.photos/seed/$entryId/400/200';
  }
}
