import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsProvider with ChangeNotifier {
  static const String _lookbackHoursKey = 'lookback_hours';
  static const String _showLlmScoreKey = 'show_llm_score';
  static const int defaultLookbackHours = 48;
  static const bool defaultShowLlmScore = true;

  late SharedPreferences _prefs;
  int _lookbackHours = defaultLookbackHours;
  bool _showLlmScore = defaultShowLlmScore;

  int get lookbackHours => _lookbackHours;
  bool get showLlmScore => _showLlmScore;

  SettingsProvider() {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    _prefs = await SharedPreferences.getInstance();
    _lookbackHours = _prefs.getInt(_lookbackHoursKey) ?? defaultLookbackHours;
    _showLlmScore = _prefs.getBool(_showLlmScoreKey) ?? defaultShowLlmScore;
    notifyListeners();
  }

  Future<void> setLookbackHours(int hours) async {
    if (hours != _lookbackHours) {
      _lookbackHours = hours;
      await _prefs.setInt(_lookbackHoursKey, hours);
      notifyListeners();
    }
  }

  Future<void> setShowLlmScore(bool show) async {
    if (show != _showLlmScore) {
      _showLlmScore = show;
      await _prefs.setBool(_showLlmScoreKey, show);
      notifyListeners();
    }
  }
}

